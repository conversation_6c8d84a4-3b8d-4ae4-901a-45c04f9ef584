<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ContractParty.
    -->
    <changeSet id="20250613081136-1" author="jhipster">
        <createTable tableName="contract_party" remarks="合同关联方实体\n存储从合同中提取的关联方信息">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID">
                <constraints nullable="false" />
            </column>
            <column name="review_id" type="bigint" remarks="关联的审查记录ID">
                <constraints nullable="false" />
            </column>
            <column name="party_name" type="varchar(256)" remarks="关联方名称">
                <constraints nullable="false" />
            </column>
            <column name="party_type" type="varchar(255)" remarks="关联方类型">
                <constraints nullable="false" />
            </column>
            <column name="party_role" type="varchar(64)" remarks="在合同中的角色">
                <constraints nullable="true" />
            </column>
            <column name="credit_code" type="varchar(32)" remarks="统一社会信用代码（企业）">
                <constraints nullable="true" />
            </column>
            <column name="registered_address" type="varchar(512)" remarks="注册地址">
                <constraints nullable="true" />
            </column>
            <column name="legal_representative" type="varchar(64)" remarks="法定代表人（企业）">
                <constraints nullable="true" />
            </column>
            <column name="contact_info" type="varchar(256)" remarks="联系方式">
                <constraints nullable="true" />
            </column>
            <column name="risk_level" type="varchar(255)" remarks="风险等级">
                <constraints nullable="true" />
            </column>
            <column name="risk_factors" type="${clobType}" remarks="风险因素（JSON数组）">
                <constraints nullable="true" />
            </column>
            <column name="compliance_issues" type="${clobType}" remarks="合规问题（JSON数组）">
                <constraints nullable="true" />
            </column>
            <column name="tianyancha_info" type="${clobType}" remarks="天眼查信息（JSON格式）">
                <constraints nullable="true" />
            </column>
            <column name="additional_info" type="${clobType}" remarks="扩展信息">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="review_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="contract_party" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="contract_party" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250613081136-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/contract_party.csv"
                  separator=";"
                  tableName="contract_party"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="review_id" type="numeric"/>
            <column name="party_name" type="string"/>
            <column name="party_type" type="string"/>
            <column name="party_role" type="string"/>
            <column name="credit_code" type="string"/>
            <column name="registered_address" type="string"/>
            <column name="legal_representative" type="string"/>
            <column name="contact_info" type="string"/>
            <column name="risk_level" type="string"/>
            <column name="risk_factors" type="clob"/>
            <column name="compliance_issues" type="clob"/>
            <column name="tianyancha_info" type="clob"/>
            <column name="additional_info" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="created_at" type="date"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
