package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.ChatLawMessageDTO;
import com.whiskerguard.ai.client.dto.ChatLawRequestDTO;
import com.whiskerguard.ai.client.dto.ChatLawResponseDTO;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.context.ApplicationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

/**
 * Client for ChatLaw API integration via 302.ai
 * 通过302.ai集成ChatLaw API的客户端
 */
@Component
public class ChatLawApiClient {

    private static final Logger log = LoggerFactory.getLogger(ChatLawApiClient.class);

    private final RestTemplate restTemplate;
    private final ApplicationContext applicationContext;

    @Value("${whiskerguard.ai.chatlaw.api.url:https://api.302.ai/v1/chat/completions}")
    private String apiUrl;

    @Value("${whiskerguard.ai.chatlaw.api.key:}")
    private String apiKey;

    @Value("${whiskerguard.ai.chatlaw.model:ChatLaw}")
    private String defaultModel;

    @Value("${whiskerguard.ai.chatlaw.max-tokens:2000}")
    private Integer defaultMaxTokens;

    @Value("${whiskerguard.ai.chatlaw.temperature:0.7}")
    private Double defaultTemperature;

    public ChatLawApiClient(RestTemplate restTemplate, ApplicationContext applicationContext) {
        this.restTemplate = restTemplate;
        this.applicationContext = applicationContext;
    }

    /**
     * Send a legal query to ChatLaw API with retry mechanism
     * 向ChatLaw API发送法律查询，带重试机制
     *
     * @param query The legal question or document content to analyze
     * @return ChatLaw API response
     */
    @Retryable(
        value = { ResourceAccessException.class, HttpServerErrorException.class },
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ChatLawResponseDTO queryLegalModel(String query) {
        log.debug("Sending query to ChatLaw API: {}", query.substring(0, Math.min(query.length(), 100)) + "...");

        try {
            // Create message for the API request
            // 为API请求创建消息
            ChatLawMessageDTO userMessage = new ChatLawMessageDTO("user", query);
            List<ChatLawMessageDTO> messages = Arrays.asList(userMessage);

            // Create request DTO
            // 创建请求DTO
            ChatLawRequestDTO request = new ChatLawRequestDTO(messages);
            request.setModel(defaultModel);
            request.setMaxTokens(defaultMaxTokens);
            request.setTemperature(defaultTemperature);

            // Set up headers
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<ChatLawRequestDTO> entity = new HttpEntity<>(request, headers);

            // Make API call
            // 进行API调用
            ResponseEntity<ChatLawResponseDTO> response = restTemplate.exchange(apiUrl, HttpMethod.POST, entity, ChatLawResponseDTO.class);

            log.debug("ChatLaw API response received successfully");
            return response.getBody();
        } catch (HttpClientErrorException e) {
            log.error("Client error when calling ChatLaw API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("ChatLaw API client error: " + e.getMessage(), e);
        } catch (HttpServerErrorException e) {
            log.error("Server error when calling ChatLaw API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("ChatLaw API server error: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error when calling ChatLaw API", e);
            throw new RuntimeException("ChatLaw API call failed: " + e.getMessage(), e);
        }
    }

    /**
     * Send a contract review request to ChatLaw API
     * 向ChatLaw API发送合同审查请求
     * 现在使用模板化方式，向后兼容硬编码方式
     *
     * @param contractContent The contract content to review
     * @return ChatLaw API response with contract analysis
     */
    public ChatLawResponseDTO reviewContract(String contractContent) {
        try {
            // 尝试使用模板化方式
            String prompt = buildTemplatedContractPrompt(contractContent);
            return queryLegalModel(prompt);
        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到硬编码方式: {}", e.getMessage());
            // 回退到原有的硬编码方式
            String prompt =
                "请对以下合同进行详细的法律审查，识别潜在的法律风险、不合规条款、模糊表述和缺失条款。请提供具体的改进建议：\n\n" +
                contractContent;
            return queryLegalModel(prompt);
        }
    }

    /**
     * Send a company policy review request to ChatLaw API
     * 向ChatLaw API发送公司制度审查请求
     * 现在使用模板化方式，向后兼容硬编码方式
     *
     * @param policyContent The company policy content to review
     * @return ChatLaw API response with policy analysis
     */
    public ChatLawResponseDTO reviewCompanyPolicy(String policyContent) {
        try {
            // 尝试使用模板化方式
            String prompt = buildTemplatedPolicyPrompt(policyContent);
            return queryLegalModel(prompt);
        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到硬编码方式: {}", e.getMessage());
            // 回退到原有的硬编码方式
            String prompt = "请对以下公司制度进行法律合规性审查，检查是否符合相关法律法规，识别可能的合规风险和改进建议：\n\n" + policyContent;
            return queryLegalModel(prompt);
        }
    }

    /**
     * 使用模板化方式构建合同审查提示词
     * Build contract review prompt using template
     */
    private String buildTemplatedContractPrompt(String contractContent) {
        try {
            PromptBuilderService promptBuilderService = applicationContext.getBean(PromptBuilderService.class);

            Map<String, Object> variables = new HashMap<>();
            variables.put("CONTRACT_CONTENT", contractContent);

            PromptBuildRequest request = PromptBuildRequest.builder()
                .templateType(PromptTemplateType.LEGAL_CONTRACT_REVIEW)
                .tenantId(1L) // 使用默认租户ID
                .variables(variables)
                .enableRagEnhancement(false)
                .buildContext("法律合同审查")
                .build();

            return promptBuilderService.buildPrompt(request);
        } catch (Exception e) {
            log.warn("无法获取PromptBuilderService，可能服务未初始化: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 使用模板化方式构建制度审查提示词
     * Build policy review prompt using template
     */
    private String buildTemplatedPolicyPrompt(String policyContent) {
        try {
            PromptBuilderService promptBuilderService = applicationContext.getBean(PromptBuilderService.class);

            Map<String, Object> variables = new HashMap<>();
            variables.put("POLICY_CONTENT", policyContent);

            PromptBuildRequest request = PromptBuildRequest.builder()
                .templateType(PromptTemplateType.LEGAL_POLICY_REVIEW)
                .tenantId(1L) // 使用默认租户ID
                .variables(variables)
                .enableRagEnhancement(false)
                .buildContext("法律制度审查")
                .build();

            return promptBuilderService.buildPrompt(request);
        } catch (Exception e) {
            log.warn("无法获取PromptBuilderService，可能服务未初始化: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Check if the ChatLaw API client is properly configured
     * 检查ChatLaw API客户端是否正确配置
     *
     * @return true if configured, false otherwise
     */
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && apiUrl != null && !apiUrl.trim().isEmpty();
    }
}
