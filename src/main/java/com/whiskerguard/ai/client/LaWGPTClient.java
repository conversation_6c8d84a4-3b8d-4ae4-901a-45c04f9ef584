package com.whiskerguard.ai.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

/**
 * Client for LaWGPT private deployment integration
 * 用于LaWGPT私有部署集成的客户端
 */
@Component
public class LaWGPTClient {

    private static final Logger log = LoggerFactory.getLogger(LaWGPTClient.class);

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${whiskerguard.ai.lawgpt.api.url:http://localhost:7860/api/predict}")
    private String apiUrl;

    @Value("${whiskerguard.ai.lawgpt.max-length:2000}")
    private Integer defaultMaxLength;

    @Value("${whiskerguard.ai.lawgpt.temperature:0.7}")
    private Double defaultTemperature;

    @Value("${whiskerguard.ai.lawgpt.top-p:0.9}")
    private Double defaultTopP;

    @Value("${whiskerguard.ai.lawgpt.enabled:false}")
    private Boolean enabled;

    public LaWGPTClient(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Send a legal query to LaWGPT private deployment
     * 向LaWGPT私有部署发送法律查询
     *
     * @param query The legal question or document content to analyze
     * @return LaWGPT response text
     */
    public String queryLegalModel(String query) {
        if (!enabled) {
            log.warn("LaWGPT client is disabled. Enable it by setting whiskerguard.ai.lawgpt.enabled=true");
            throw new RuntimeException("LaWGPT client is disabled");
        }

        log.debug("Sending query to LaWGPT: {}", query.substring(0, Math.min(query.length(), 100)) + "...");

        try {
            // Create request payload for LaWGPT API
            // 为LaWGPT API创建请求负载
            Map<String, Object> requestData = new HashMap<>();
            requestData.put(
                "data",
                new Object[] {
                    query, // input text
                    defaultMaxLength, // max_length
                    defaultTemperature, // temperature
                    defaultTopP, // top_p
                    1, // top_k
                    1.0, // repetition_penalty
                    false, // do_sample
                }
            );

            // Set up headers
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            // Make API call
            // 进行API调用
            ResponseEntity<String> response = restTemplate.exchange(apiUrl, HttpMethod.POST, entity, String.class);

            // Parse response
            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            JsonNode dataNode = responseJson.get("data");

            if (dataNode != null && dataNode.isArray() && dataNode.size() > 0) {
                String result = dataNode.get(0).asText();
                log.debug("LaWGPT response received successfully");
                return result;
            } else {
                log.error("Invalid response format from LaWGPT API");
                throw new RuntimeException("Invalid response format from LaWGPT API");
            }
        } catch (HttpClientErrorException e) {
            log.error("Client error when calling LaWGPT API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("LaWGPT API client error: " + e.getMessage(), e);
        } catch (HttpServerErrorException e) {
            log.error("Server error when calling LaWGPT API: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new RuntimeException("LaWGPT API server error: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error when calling LaWGPT API", e);
            throw new RuntimeException("LaWGPT API call failed: " + e.getMessage(), e);
        }
    }

    /**
     * Send a contract review request to LaWGPT
     * 向LaWGPT发送合同审查请求
     * 现在使用模板化方式，向后兼容硬编码方式
     *
     * @param contractContent The contract content to review
     * @return LaWGPT response with contract analysis
     */
    public String reviewContract(String contractContent) {
        try {
            // 尝试使用模板化方式
            String prompt = buildTemplatedContractPrompt(contractContent);
            return queryLegalModel(prompt);
        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到硬编码方式: {}", e.getMessage());
            // 回退到原有的硬编码方式
            String prompt =
                "请对以下合同进行详细的法律审查，识别潜在的法律风险、不合规条款、模糊表述和缺失条款。请提供具体的改进建议：\n\n" +
                contractContent;
            return queryLegalModel(prompt);
        }
    }

    /**
     * 使用模板化方式构建合同审查提示词
     */
    private String buildTemplatedContractPrompt(String contractContent) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("CONTRACT_CONTENT", contractContent);

        PromptBuildRequest request = PromptBuildRequest.builder()
            .templateType(PromptTemplateType.LEGAL_CONTRACT_REVIEW)
            .tenantId(1L) // 使用默认租户ID
            .variables(variables)
            .enableRagEnhancement(false)
            .buildContext("法律合同审查")
            .build();

        return promptBuilderService.buildPrompt(request);
    }

    /**
     * Send a company policy review request to LaWGPT
     * 向LaWGPT发送公司制度审查请求
     * 现在使用模板化方式，向后兼容硬编码方式
     *
     * @param policyContent The company policy content to review
     * @return LaWGPT response with policy analysis
     */
    public String reviewCompanyPolicy(String policyContent) {
        try {
            // 尝试使用模板化方式
            String prompt = buildTemplatedPolicyPrompt(policyContent);
            return queryLegalModel(prompt);
        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到硬编码方式: {}", e.getMessage());
            // 回退到原有的硬编码方式
            String prompt = "请对以下公司制度进行法律合规性审查，检查是否符合相关法律法规，识别可能的合规风险和改进建议：\n\n" + policyContent;
            return queryLegalModel(prompt);
        }
    }

    /**
     * 使用模板化方式构建制度审查提示词
     */
    private String buildTemplatedPolicyPrompt(String policyContent) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("POLICY_CONTENT", policyContent);

        PromptBuildRequest request = PromptBuildRequest.builder()
            .templateType(PromptTemplateType.LEGAL_POLICY_REVIEW)
            .tenantId(1L) // 使用默认租户ID
            .variables(variables)
            .enableRagEnhancement(false)
            .buildContext("法律制度审查")
            .build();

        return promptBuilderService.buildPrompt(request);
    }

    /**
     * Check if the LaWGPT client is properly configured and enabled
     * 检查LaWGPT客户端是否正确配置并启用
     *
     * @return true if configured and enabled, false otherwise
     */
    public boolean isConfigured() {
        return enabled && apiUrl != null && !apiUrl.trim().isEmpty();
    }

    /**
     * Test connectivity to LaWGPT deployment with timeout
     * 测试与LaWGPT部署的连接性，带超时控制
     *
     * @return true if LaWGPT is accessible, false otherwise
     */
    public boolean testConnectivity() {
        if (!isConfigured()) {
            log.debug("LaWGPT not configured, connectivity test skipped");
            return false;
        }

        try {
            log.debug("Testing LaWGPT connectivity to: {}", apiUrl);

            // 使用简短的测试查询以减少响应时间
            String testQuery = "测试连接";

            // 创建专门用于健康检查的请求，使用较短的参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put(
                "data",
                new Object[] {
                    testQuery,
                    50, // 较短的max_length用于快速测试
                    0.1, // 较低的temperature
                    0.5, // 较低的top_p
                    1, // top_k
                    1.0, // repetition_penalty
                    false, // do_sample
                }
            );

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            ResponseEntity<String> response = restTemplate.exchange(apiUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseJson = objectMapper.readTree(response.getBody());
                JsonNode dataNode = responseJson.get("data");

                if (dataNode != null && dataNode.isArray() && dataNode.size() > 0) {
                    String result = dataNode.get(0).asText();
                    boolean isHealthy = result != null && !result.trim().isEmpty();
                    log.debug("LaWGPT connectivity test {}", isHealthy ? "passed" : "failed");
                    return isHealthy;
                }
            }

            log.warn("LaWGPT connectivity test failed: Invalid response format");
            return false;
        } catch (Exception e) {
            log.warn("LaWGPT connectivity test failed: {}", e.getMessage());
            return false;
        }
    }
}
