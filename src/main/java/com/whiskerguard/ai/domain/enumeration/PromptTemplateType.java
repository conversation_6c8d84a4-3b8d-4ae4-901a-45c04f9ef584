package com.whiskerguard.ai.domain.enumeration;

/**
 * 提示词模板类型枚举
 * <p>
 * 定义了系统支持的各种提示词模板类型，用于分类管理不同业务场景的提示词模板。
 * 每种类型对应特定的业务场景和AI服务功能。
 */
public enum PromptTemplateType {
    
    /**
     * 合同审查相关模板
     */
    CONTRACT_REVIEW("合同审查"),
    
    /**
     * 合同综合审查模板
     */
    CONTRACT_COMPREHENSIVE_REVIEW("合同综合审查"),
    
    /**
     * 合同法律合规审查模板
     */
    CONTRACT_LEGAL_COMPLIANCE("合同法律合规"),
    
    /**
     * 合同风险评估模板
     */
    CONTRACT_RISK_ASSESSMENT("合同风险评估"),
    
    /**
     * 合同财务条款审查模板
     */
    CONTRACT_FINANCIAL_TERMS("合同财务条款"),
    
    /**
     * 制度审查相关模板
     */
    POLICY_REVIEW("制度审查"),
    
    /**
     * 法规转制度模板
     */
    POLICY_REGULATORY_CONVERSION("法规转制度"),
    
    /**
     * 内部制度审查模板
     */
    POLICY_INTERNAL_REVIEW("内部制度审查"),
    
    /**
     * 合规检查模板
     */
    POLICY_COMPLIANCE_CHECK("合规检查"),
    
    /**
     * 通用AI分析模板
     */
    GENERAL_ANALYSIS("通用分析"),
    
    /**
     * 通用AI生成模板
     */
    GENERAL_GENERATION("通用生成"),
    
    /**
     * 通用AI提取模板
     */
    GENERAL_EXTRACTION("通用提取");
    
    private final String displayName;
    
    PromptTemplateType(String displayName) {
        this.displayName = displayName;
    }
    
    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据显示名称获取枚举值
     *
     * @param displayName 显示名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static PromptTemplateType fromDisplayName(String displayName) {
        for (PromptTemplateType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return null;
    }
}
