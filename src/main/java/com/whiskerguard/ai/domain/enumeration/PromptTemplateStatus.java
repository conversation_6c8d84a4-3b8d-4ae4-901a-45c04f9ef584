package com.whiskerguard.ai.domain.enumeration;

/**
 * 提示词模板状态枚举
 * <p>
 * 定义了提示词模板的各种状态，用于管理模板的生命周期。
 * 支持模板的创建、测试、发布、停用等状态管理。
 */
public enum PromptTemplateStatus {
    
    /**
     * 草稿状态 - 模板正在编辑中，尚未完成
     */
    DRAFT("草稿"),
    
    /**
     * 测试状态 - 模板已完成编辑，正在测试验证中
     */
    TESTING("测试中"),
    
    /**
     * 已发布状态 - 模板已通过测试，正式发布使用
     */
    PUBLISHED("已发布"),
    
    /**
     * 已停用状态 - 模板已停止使用，但保留历史记录
     */
    DISABLED("已停用"),
    
    /**
     * 已归档状态 - 模板已归档，不再使用
     */
    ARCHIVED("已归档");
    
    private final String displayName;
    
    PromptTemplateStatus(String displayName) {
        this.displayName = displayName;
    }
    
    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据显示名称获取枚举值
     *
     * @param displayName 显示名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static PromptTemplateStatus fromDisplayName(String displayName) {
        for (PromptTemplateStatus status : values()) {
            if (status.displayName.equals(displayName)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为可用状态
     *
     * @return 如果状态为已发布则返回true，否则返回false
     */
    public boolean isActive() {
        return this == PUBLISHED;
    }
    
    /**
     * 检查是否可以编辑
     *
     * @return 如果状态为草稿或测试中则返回true，否则返回false
     */
    public boolean isEditable() {
        return this == DRAFT || this == TESTING;
    }
}
