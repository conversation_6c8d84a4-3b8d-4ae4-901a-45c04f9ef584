package com.whiskerguard.ai.domain.enumeration;

/**
 * 变量类型枚举
 * <p>
 * 定义了提示词模板中支持的各种变量类型，用于分类管理不同来源和用途的变量。
 * 支持系统变量、业务变量、RAG变量和自定义变量等多种类型。
 */
public enum VariableType {
    
    /**
     * 系统变量 - 由系统自动提供的变量
     * 如：租户ID、当前时间、用户信息等
     */
    SYSTEM("系统变量"),
    
    /**
     * 业务变量 - 业务逻辑相关的变量
     * 如：合同类型、公司信息、制度类型等
     */
    BUSINESS("业务变量"),
    
    /**
     * RAG变量 - 由RAG检索服务提供的变量
     * 如：增强上下文、相关案例、法规解释等
     */
    RAG("RAG变量"),
    
    /**
     * 自定义变量 - 租户自定义的变量
     * 如：企业特定术语、行业专用词汇等
     */
    CUSTOM("自定义变量"),
    
    /**
     * 计算变量 - 通过计算得出的变量
     * 如：风险评分、复杂度指标等
     */
    COMPUTED("计算变量"),
    
    /**
     * 外部变量 - 来自外部服务的变量
     * 如：第三方API数据、外部系统信息等
     */
    EXTERNAL("外部变量");
    
    private final String displayName;
    
    VariableType(String displayName) {
        this.displayName = displayName;
    }
    
    /**
     * 获取显示名称
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据显示名称获取枚举值
     *
     * @param displayName 显示名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static VariableType fromDisplayName(String displayName) {
        for (VariableType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为系统内置变量类型
     *
     * @return 如果是系统或业务变量则返回true，否则返回false
     */
    public boolean isBuiltIn() {
        return this == SYSTEM || this == BUSINESS;
    }
    
    /**
     * 检查是否需要外部数据源
     *
     * @return 如果是RAG、外部或计算变量则返回true，否则返回false
     */
    public boolean requiresExternalData() {
        return this == RAG || this == EXTERNAL || this == COMPUTED;
    }
}
