package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 提示词模板版本实体
 * <p>
 * 用于管理提示词模板的版本历史，支持版本回滚、A/B测试等功能。
 * 每个版本保存完整的模板内容和变量定义，确保历史版本的完整性。
 */
@Entity
@Table(name = "prompt_template_version")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplateVersion extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    /**
     * 版本号
     */
    @NotNull
    @Min(value = 1)
    @Column(name = "version_number", nullable = false)
    private Integer versionNumber;

    /**
     * 版本名称
     */
    @Size(max = 200)
    @Column(name = "version_name", length = 200)
    private String versionName;

    /**
     * 版本描述
     */
    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 模板内容
     */
    @NotNull
    @Lob
    @Column(name = "content", nullable = false)
    private String content;

    /**
     * 变量定义（JSON格式）
     */
    @Lob
    @Column(name = "variables_definition")
    private String variablesDefinition;

    /**
     * 版本状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PromptTemplateStatus status;

    /**
     * 是否为当前活跃版本
     */
    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    /**
     * 创建者ID
     */
    @Column(name = "created_by_id")
    private Long createdById;

    /**
     * 版本变更说明
     */
    @Size(max = 1000)
    @Column(name = "change_log", length = 1000)
    private String changeLog;

    /**
     * 使用次数统计
     */
    @Min(value = 0)
    @Column(name = "usage_count")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    /**
     * 性能评分
     * 基于使用效果的评分，用于A/B测试
     */
    @DecimalMin(value = "0")
    @DecimalMax(value = "10")
    @Column(name = "performance_score")
    private Double performanceScore;

    /**
     * 所属提示词模板
     */
    @ManyToOne(optional = false)
    @NotNull
    @JsonIgnoreProperties(value = { "variables", "versions" }, allowSetters = true)
    private PromptTemplate promptTemplate;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromptTemplateVersion id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersionNumber() {
        return this.versionNumber;
    }

    public PromptTemplateVersion versionNumber(Integer versionNumber) {
        this.setVersionNumber(versionNumber);
        return this;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getVersionName() {
        return this.versionName;
    }

    public PromptTemplateVersion versionName(String versionName) {
        this.setVersionName(versionName);
        return this;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getDescription() {
        return this.description;
    }

    public PromptTemplateVersion description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return this.content;
    }

    public PromptTemplateVersion content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVariablesDefinition() {
        return this.variablesDefinition;
    }

    public PromptTemplateVersion variablesDefinition(String variablesDefinition) {
        this.setVariablesDefinition(variablesDefinition);
        return this;
    }

    public void setVariablesDefinition(String variablesDefinition) {
        this.variablesDefinition = variablesDefinition;
    }

    public PromptTemplateStatus getStatus() {
        return this.status;
    }

    public PromptTemplateVersion status(PromptTemplateStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Boolean getIsActive() {
        return this.isActive;
    }

    public PromptTemplateVersion isActive(Boolean isActive) {
        this.setIsActive(isActive);
        return this;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Long getCreatedById() {
        return this.createdById;
    }

    public PromptTemplateVersion createdById(Long createdById) {
        this.setCreatedById(createdById);
        return this;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public String getChangeLog() {
        return this.changeLog;
    }

    public PromptTemplateVersion changeLog(String changeLog) {
        this.setChangeLog(changeLog);
        return this;
    }

    public void setChangeLog(String changeLog) {
        this.changeLog = changeLog;
    }

    public Long getUsageCount() {
        return this.usageCount;
    }

    public PromptTemplateVersion usageCount(Long usageCount) {
        this.setUsageCount(usageCount);
        return this;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return this.lastUsedAt;
    }

    public PromptTemplateVersion lastUsedAt(Instant lastUsedAt) {
        this.setLastUsedAt(lastUsedAt);
        return this;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Double getPerformanceScore() {
        return this.performanceScore;
    }

    public PromptTemplateVersion performanceScore(Double performanceScore) {
        this.setPerformanceScore(performanceScore);
        return this;
    }

    public void setPerformanceScore(Double performanceScore) {
        this.performanceScore = performanceScore;
    }

    public PromptTemplate getPromptTemplate() {
        return this.promptTemplate;
    }

    public void setPromptTemplate(PromptTemplate promptTemplate) {
        this.promptTemplate = promptTemplate;
    }

    public PromptTemplateVersion promptTemplate(PromptTemplate promptTemplate) {
        this.setPromptTemplate(promptTemplate);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplateVersion)) {
            return false;
        }
        return getId() != null && getId().equals(((PromptTemplateVersion) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplateVersion{" +
            "id=" + getId() +
            ", versionNumber=" + getVersionNumber() +
            ", versionName='" + getVersionName() + "'" +
            ", description='" + getDescription() + "'" +
            ", content='" + getContent() + "'" +
            ", variablesDefinition='" + getVariablesDefinition() + "'" +
            ", status='" + getStatus() + "'" +
            ", isActive='" + getIsActive() + "'" +
            ", createdById=" + getCreatedById() +
            ", changeLog='" + getChangeLog() + "'" +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            ", performanceScore=" + getPerformanceScore() +
            "}";
    }
}
