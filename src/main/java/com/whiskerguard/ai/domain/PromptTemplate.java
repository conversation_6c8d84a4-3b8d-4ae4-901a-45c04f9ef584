package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 提示词模板实体
 * <p>
 * 用于存储和管理AI服务中使用的提示词模板。支持多租户、版本控制、
 * 分类管理等功能。每个模板可以包含多个变量，支持动态内容替换。
 */
@Entity
@Table(name = "prompt_template")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromptTemplate extends AbstractAuditingEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    /**
     * 模板唯一标识键
     * 用于程序中引用模板，必须唯一
     */
    @NotNull
    @Size(min = 1, max = 100)
    @Column(name = "template_key", length = 100, nullable = false, unique = true)
    private String templateKey;

    /**
     * 模板名称
     */
    @NotNull
    @Size(min = 1, max = 200)
    @Column(name = "name", length = 200, nullable = false)
    private String name;

    /**
     * 模板描述
     */
    @Size(max = 1000)
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 模板类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "template_type", nullable = false)
    private PromptTemplateType templateType;

    /**
     * 模板内容
     * 支持变量占位符，如 {{VARIABLE_NAME}}
     */
    @NotNull
    @Lob
    @Column(name = "content", nullable = false)
    private String content;

    /**
     * 模板状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PromptTemplateStatus status;

    /**
     * 版本号
     */
    @NotNull
    @Min(value = 1)
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 是否为系统默认模板
     */
    @NotNull
    @Column(name = "is_system_default", nullable = false)
    private Boolean isSystemDefault;

    /**
     * 租户ID
     * null表示系统级模板，非null表示租户专用模板
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 创建者ID
     */
    @Column(name = "created_by_id")
    private Long createdById;

    /**
     * 最后修改者ID
     */
    @Column(name = "last_modified_by_id")
    private Long lastModifiedById;

    /**
     * 使用次数统计
     */
    @Min(value = 0)
    @Column(name = "usage_count")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    /**
     * 模板变量集合
     */
    @OneToMany(mappedBy = "promptTemplate", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "promptTemplate" }, allowSetters = true)
    private Set<PromptTemplateVariable> variables = new HashSet<>();

    /**
     * 模板版本集合
     */
    @OneToMany(mappedBy = "promptTemplate", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "promptTemplate" }, allowSetters = true)
    private Set<PromptTemplateVersion> versions = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromptTemplate id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateKey() {
        return this.templateKey;
    }

    public PromptTemplate templateKey(String templateKey) {
        this.setTemplateKey(templateKey);
        return this;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public String getName() {
        return this.name;
    }

    public PromptTemplate name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public PromptTemplate description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PromptTemplateType getTemplateType() {
        return this.templateType;
    }

    public PromptTemplate templateType(PromptTemplateType templateType) {
        this.setTemplateType(templateType);
        return this;
    }

    public void setTemplateType(PromptTemplateType templateType) {
        this.templateType = templateType;
    }

    public String getContent() {
        return this.content;
    }

    public PromptTemplate content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public PromptTemplateStatus getStatus() {
        return this.status;
    }

    public PromptTemplate status(PromptTemplateStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromptTemplateStatus status) {
        this.status = status;
    }

    public Integer getVersion() {
        return this.version;
    }

    public PromptTemplate version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Boolean getIsSystemDefault() {
        return this.isSystemDefault;
    }

    public PromptTemplate isSystemDefault(Boolean isSystemDefault) {
        this.setIsSystemDefault(isSystemDefault);
        return this;
    }

    public void setIsSystemDefault(Boolean isSystemDefault) {
        this.isSystemDefault = isSystemDefault;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public PromptTemplate tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCreatedById() {
        return this.createdById;
    }

    public PromptTemplate createdById(Long createdById) {
        this.setCreatedById(createdById);
        return this;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public Long getLastModifiedById() {
        return this.lastModifiedById;
    }

    public PromptTemplate lastModifiedById(Long lastModifiedById) {
        this.setLastModifiedById(lastModifiedById);
        return this;
    }

    public void setLastModifiedById(Long lastModifiedById) {
        this.lastModifiedById = lastModifiedById;
    }

    public Long getUsageCount() {
        return this.usageCount;
    }

    public PromptTemplate usageCount(Long usageCount) {
        this.setUsageCount(usageCount);
        return this;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public Instant getLastUsedAt() {
        return this.lastUsedAt;
    }

    public PromptTemplate lastUsedAt(Instant lastUsedAt) {
        this.setLastUsedAt(lastUsedAt);
        return this;
    }

    public void setLastUsedAt(Instant lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
    }

    public Set<PromptTemplateVariable> getVariables() {
        return this.variables;
    }

    public void setVariables(Set<PromptTemplateVariable> promptTemplateVariables) {
        if (this.variables != null) {
            this.variables.forEach(i -> i.setPromptTemplate(null));
        }
        if (promptTemplateVariables != null) {
            promptTemplateVariables.forEach(i -> i.setPromptTemplate(this));
        }
        this.variables = promptTemplateVariables;
    }

    public PromptTemplate variables(Set<PromptTemplateVariable> promptTemplateVariables) {
        this.setVariables(promptTemplateVariables);
        return this;
    }

    public PromptTemplate addVariable(PromptTemplateVariable promptTemplateVariable) {
        this.variables.add(promptTemplateVariable);
        promptTemplateVariable.setPromptTemplate(this);
        return this;
    }

    public PromptTemplate removeVariable(PromptTemplateVariable promptTemplateVariable) {
        this.variables.remove(promptTemplateVariable);
        promptTemplateVariable.setPromptTemplate(null);
        return this;
    }

    public Set<PromptTemplateVersion> getVersions() {
        return this.versions;
    }

    public void setVersions(Set<PromptTemplateVersion> promptTemplateVersions) {
        if (this.versions != null) {
            this.versions.forEach(i -> i.setPromptTemplate(null));
        }
        if (promptTemplateVersions != null) {
            promptTemplateVersions.forEach(i -> i.setPromptTemplate(this));
        }
        this.versions = promptTemplateVersions;
    }

    public PromptTemplate versions(Set<PromptTemplateVersion> promptTemplateVersions) {
        this.setVersions(promptTemplateVersions);
        return this;
    }

    public PromptTemplate addVersion(PromptTemplateVersion promptTemplateVersion) {
        this.versions.add(promptTemplateVersion);
        promptTemplateVersion.setPromptTemplate(this);
        return this;
    }

    public PromptTemplate removeVersion(PromptTemplateVersion promptTemplateVersion) {
        this.versions.remove(promptTemplateVersion);
        promptTemplateVersion.setPromptTemplate(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromptTemplate)) {
            return false;
        }
        return getId() != null && getId().equals(((PromptTemplate) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromptTemplate{" +
            "id=" + getId() +
            ", templateKey='" + getTemplateKey() + "'" +
            ", name='" + getName() + "'" +
            ", description='" + getDescription() + "'" +
            ", templateType='" + getTemplateType() + "'" +
            ", content='" + getContent() + "'" +
            ", status='" + getStatus() + "'" +
            ", version=" + getVersion() +
            ", isSystemDefault='" + getIsSystemDefault() + "'" +
            ", tenantId=" + getTenantId() +
            ", createdById=" + getCreatedById() +
            ", lastModifiedById=" + getLastModifiedById() +
            ", usageCount=" + getUsageCount() +
            ", lastUsedAt='" + getLastUsedAt() + "'" +
            "}";
    }
}
