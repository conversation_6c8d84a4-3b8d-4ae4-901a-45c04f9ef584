package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 提示词模板数据访问接口
 * <p>
 * 提供提示词模板的数据库操作方法，包括基本的CRUD操作、
 * 多租户查询、模板优先级查询等功能。
 */
@Repository
public interface PromptTemplateRepository extends JpaRepository<PromptTemplate, Long>, JpaSpecificationExecutor<PromptTemplate> {
    
    /**
     * 根据模板键查找模板
     *
     * @param templateKey 模板键
     * @return 模板对象
     */
    Optional<PromptTemplate> findByTemplateKey(String templateKey);
    
    /**
     * 根据租户ID和模板类型查找已发布的模板
     * 优先返回租户专用模板，如果没有则返回系统默认模板
     *
     * @param tenantId 租户ID
     * @param templateType 模板类型
     * @param status 模板状态
     * @return 模板列表，按优先级排序
     */
    @Query("""
        SELECT pt FROM PromptTemplate pt 
        WHERE pt.templateType = :templateType 
        AND pt.status = :status 
        AND (pt.tenantId = :tenantId OR pt.isSystemDefault = true)
        ORDER BY 
            CASE WHEN pt.tenantId = :tenantId THEN 1 ELSE 2 END,
            pt.version DESC
        """)
    List<PromptTemplate> findByTenantIdAndTemplateTypeAndStatusOrderByPriority(
        @Param("tenantId") Long tenantId,
        @Param("templateType") PromptTemplateType templateType,
        @Param("status") PromptTemplateStatus status
    );
    
    /**
     * 根据租户ID查找所有可用模板
     *
     * @param tenantId 租户ID
     * @param status 模板状态
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    @Query("""
        SELECT pt FROM PromptTemplate pt 
        WHERE pt.status = :status 
        AND (pt.tenantId = :tenantId OR pt.isSystemDefault = true)
        ORDER BY pt.templateType, pt.name
        """)
    Page<PromptTemplate> findAvailableTemplatesByTenantId(
        @Param("tenantId") Long tenantId,
        @Param("status") PromptTemplateStatus status,
        Pageable pageable
    );
    
    /**
     * 查找系统默认模板
     *
     * @param templateType 模板类型
     * @param status 模板状态
     * @return 系统默认模板
     */
    Optional<PromptTemplate> findByTemplateTypeAndStatusAndIsSystemDefaultTrue(
        PromptTemplateType templateType,
        PromptTemplateStatus status
    );
    
    /**
     * 根据租户ID和模板类型查找模板
     *
     * @param tenantId 租户ID
     * @param templateType 模板类型
     * @return 模板列表
     */
    List<PromptTemplate> findByTenantIdAndTemplateType(Long tenantId, PromptTemplateType templateType);
    
    /**
     * 查找租户的所有模板
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    Page<PromptTemplate> findByTenantId(Long tenantId, Pageable pageable);
    
    /**
     * 查找系统默认模板
     *
     * @param pageable 分页参数
     * @return 分页的系统默认模板列表
     */
    Page<PromptTemplate> findByIsSystemDefaultTrue(Pageable pageable);
    
    /**
     * 根据状态查找模板
     *
     * @param status 模板状态
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    Page<PromptTemplate> findByStatus(PromptTemplateStatus status, Pageable pageable);
    
    /**
     * 根据模板类型查找模板
     *
     * @param templateType 模板类型
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    Page<PromptTemplate> findByTemplateType(PromptTemplateType templateType, Pageable pageable);
    
    /**
     * 检查模板键是否已存在
     *
     * @param templateKey 模板键
     * @return 是否存在
     */
    boolean existsByTemplateKey(String templateKey);
    
    /**
     * 检查租户是否有指定类型的自定义模板
     *
     * @param tenantId 租户ID
     * @param templateType 模板类型
     * @return 是否存在
     */
    boolean existsByTenantIdAndTemplateType(Long tenantId, PromptTemplateType templateType);
    
    /**
     * 更新模板使用统计
     *
     * @param templateId 模板ID
     */
    @Modifying
    @Query("""
        UPDATE PromptTemplate pt 
        SET pt.usageCount = COALESCE(pt.usageCount, 0) + 1,
            pt.lastUsedAt = CURRENT_TIMESTAMP
        WHERE pt.id = :templateId
        """)
    void incrementUsageCount(@Param("templateId") Long templateId);
    
    /**
     * 根据名称模糊查询模板
     *
     * @param name 模板名称关键字
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    Page<PromptTemplate> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * 根据租户ID和名称模糊查询模板
     *
     * @param tenantId 租户ID
     * @param name 模板名称关键字
     * @param pageable 分页参数
     * @return 分页的模板列表
     */
    @Query("""
        SELECT pt FROM PromptTemplate pt 
        WHERE (pt.tenantId = :tenantId OR pt.isSystemDefault = true)
        AND LOWER(pt.name) LIKE LOWER(CONCAT('%', :name, '%'))
        ORDER BY 
            CASE WHEN pt.tenantId = :tenantId THEN 1 ELSE 2 END,
            pt.name
        """)
    Page<PromptTemplate> findByTenantIdAndNameContaining(
        @Param("tenantId") Long tenantId,
        @Param("name") String name,
        Pageable pageable
    );
}
