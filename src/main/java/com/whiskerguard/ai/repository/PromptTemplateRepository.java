package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import java.util.List;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the PromptTemplate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PromptTemplateRepository extends JpaRepository<PromptTemplate, Long> {

    /**
     * 根据模板键和租户ID查找模板
     */
    List<PromptTemplate> findByTemplateKeyAndTenantIdAndStatus(String templateKey, Long tenantId, PromptTemplateStatus status);

    /**
     * 根据模板键查找系统默认模板
     */
    List<PromptTemplate> findByTemplateKeyAndIsSystemDefaultTrueAndStatus(String templateKey, PromptTemplateStatus status);

    /**
     * 根据模板类型和租户ID查找模板
     */
    List<PromptTemplate> findByTemplateTypeAndTenantIdAndStatus(PromptTemplateType templateType, Long tenantId, PromptTemplateStatus status);

    /**
     * 根据模板类型查找系统默认模板
     */
    List<PromptTemplate> findByTemplateTypeAndIsSystemDefaultTrueAndStatus(PromptTemplateType templateType, PromptTemplateStatus status);
}
