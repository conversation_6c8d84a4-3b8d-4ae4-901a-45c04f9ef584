package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplateVersion}.
 */
public interface PromptTemplateVersionService {
    /**
     * Save a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO the entity to save.
     * @return the persisted entity.
     */
    PromptTemplateVersionDTO save(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * Updates a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO the entity to update.
     * @return the persisted entity.
     */
    PromptTemplateVersionDTO update(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * Partially updates a promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PromptTemplateVersionDTO> partialUpdate(PromptTemplateVersionDTO promptTemplateVersionDTO);

    /**
     * Get all the promptTemplateVersions.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PromptTemplateVersionDTO> findAll(Pageable pageable);

    /**
     * Get the "id" promptTemplateVersion.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PromptTemplateVersionDTO> findOne(Long id);

    /**
     * Delete the "id" promptTemplateVersion.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
