package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.TenantPromptConfigDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.TenantPromptConfig}.
 */
public interface TenantPromptConfigService {
    /**
     * Save a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO the entity to save.
     * @return the persisted entity.
     */
    TenantPromptConfigDTO save(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * Updates a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO the entity to update.
     * @return the persisted entity.
     */
    TenantPromptConfigDTO update(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * Partially updates a tenantPromptConfig.
     *
     * @param tenantPromptConfigDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<TenantPromptConfigDTO> partialUpdate(TenantPromptConfigDTO tenantPromptConfigDTO);

    /**
     * Get all the tenantPromptConfigs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TenantPromptConfigDTO> findAll(Pageable pageable);

    /**
     * Get the "id" tenantPromptConfig.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<TenantPromptConfigDTO> findOne(Long id);

    /**
     * Delete the "id" tenantPromptConfig.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
