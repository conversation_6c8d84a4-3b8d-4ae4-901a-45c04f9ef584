/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiStreamRequestDTO.java
 * 包    名：com.whiskerguard.ai.service.dto
 * 描    述：AI流式调用请求DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.dto;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * DTO for streaming invocation of AI tools via the whiskerguard-ai-service.
 * 用于通过whiskerguard-ai-service流式调用AI工具的数据传输对象。
 */
public class AiStreamRequestDTO implements Serializable {

    // 序列化版本ID
    private static final long serialVersionUID = 1L;

    /**
     * Identifier of the AI tool to invoke (e.g., "chatgpt", "deepseek").
     * AI工具的标识符（例如："chatgpt", "deepseek"）。
     */
    @NotNull
    private String toolKey;

    /**
     * Prompt or input text for the AI tool.
     * 提供给AI工具的提示或输入文本。
     */
    @NotNull
    private String prompt;

    /**
     * Optional metadata or parameters for the invocation.
     * 调用时的可选元数据或参数。
     */
    private Map<String, Object> metadata;

    /**
     * Tenant identifier for multi-tenant scenarios.
     * 多租户场景下的租户标识符。
     */
    private Long tenantId;

    /**
     * Employee identifier for tracking user requests.
     * 员工标识符，用于跟踪用户请求。
     */
    @NotNull
    private Long employeeId;

    /**
     * Flag to enable streaming mode.
     * 是否启用流式模式的标志。
     */
    private boolean streaming = true;

    public AiStreamRequestDTO() {
        // Default constructor 默认构造函数
    }

    public AiStreamRequestDTO(String toolKey, String prompt, Map<String, Object> metadata, Long tenantId) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.tenantId = tenantId;
    }

    public AiStreamRequestDTO(String toolKey, String prompt, Map<String, Object> metadata, Long tenantId, Long employeeId) {
        this.toolKey = toolKey;
        this.prompt = prompt;
        this.metadata = metadata;
        this.tenantId = tenantId;
        this.employeeId = employeeId;
    }

    public String getToolKey() {
        return toolKey;
    }

    public void setToolKey(String toolKey) {
        this.toolKey = toolKey;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public boolean isStreaming() {
        return streaming;
    }

    public void setStreaming(boolean streaming) {
        this.streaming = streaming;
    }

    @Override
    public String toString() {
        return (
            "AiStreamRequestDTO{" +
            "toolKey='" +
            toolKey +
            '\'' +
            ", prompt='" +
            prompt +
            '\'' +
            ", metadata=" +
            metadata +
            ", tenantId=" +
            tenantId +
            ", employeeId=" +
            employeeId +
            ", streaming=" +
            streaming +
            '}'
        );
    }
}
