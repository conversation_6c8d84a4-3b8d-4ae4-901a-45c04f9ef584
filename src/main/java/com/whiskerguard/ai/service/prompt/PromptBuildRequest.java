package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import java.util.Map;

/**
 * 提示词构建请求对象
 * <p>
 * 封装提示词构建所需的所有参数，支持灵活的配置选项。
 */
public class PromptBuildRequest {

    /**
     * 模板键（优先使用）
     */
    private String templateKey;

    /**
     * 模板类型（当templateKey为空时使用）
     */
    private PromptTemplateType templateType;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 变量值映射
     */
    private Map<String, Object> variables;

    /**
     * 是否启用RAG增强
     */
    private boolean enableRagEnhancement = false;

    /**
     * 是否使用缓存
     */
    private boolean useCache = true;

    /**
     * 自定义配置覆盖
     */
    private Map<String, Object> configOverrides;

    /**
     * 构建上下文信息
     */
    private String buildContext;

    // 构造函数
    public PromptBuildRequest() {}

    public PromptBuildRequest(String templateKey, Long tenantId, Map<String, Object> variables) {
        this.templateKey = templateKey;
        this.tenantId = tenantId;
        this.variables = variables;
    }

    public PromptBuildRequest(PromptTemplateType templateType, Long tenantId, Map<String, Object> variables) {
        this.templateType = templateType;
        this.tenantId = tenantId;
        this.variables = variables;
    }

    // Builder 模式
    public static PromptBuildRequest builder() {
        return new PromptBuildRequest();
    }

    public PromptBuildRequest templateKey(String templateKey) {
        this.templateKey = templateKey;
        return this;
    }

    public PromptBuildRequest templateType(PromptTemplateType templateType) {
        this.templateType = templateType;
        return this;
    }

    public PromptBuildRequest tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public PromptBuildRequest variables(Map<String, Object> variables) {
        this.variables = variables;
        return this;
    }

    public PromptBuildRequest enableRagEnhancement(boolean enableRagEnhancement) {
        this.enableRagEnhancement = enableRagEnhancement;
        return this;
    }

    public PromptBuildRequest useCache(boolean useCache) {
        this.useCache = useCache;
        return this;
    }

    public PromptBuildRequest configOverrides(Map<String, Object> configOverrides) {
        this.configOverrides = configOverrides;
        return this;
    }

    public PromptBuildRequest buildContext(String buildContext) {
        this.buildContext = buildContext;
        return this;
    }

    public PromptBuildRequest build() {
        return this;
    }

    // Getters and Setters
    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public PromptTemplateType getTemplateType() {
        return templateType;
    }

    public void setTemplateType(PromptTemplateType templateType) {
        this.templateType = templateType;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public boolean isEnableRagEnhancement() {
        return enableRagEnhancement;
    }

    public void setEnableRagEnhancement(boolean enableRagEnhancement) {
        this.enableRagEnhancement = enableRagEnhancement;
    }

    public boolean isUseCache() {
        return useCache;
    }

    public void setUseCache(boolean useCache) {
        this.useCache = useCache;
    }

    public Map<String, Object> getConfigOverrides() {
        return configOverrides;
    }

    public void setConfigOverrides(Map<String, Object> configOverrides) {
        this.configOverrides = configOverrides;
    }

    public String getBuildContext() {
        return buildContext;
    }

    public void setBuildContext(String buildContext) {
        this.buildContext = buildContext;
    }

    @Override
    public String toString() {
        return "PromptBuildRequest{" +
            "templateKey='" + templateKey + '\'' +
            ", templateType=" + templateType +
            ", tenantId=" + tenantId +
            ", enableRagEnhancement=" + enableRagEnhancement +
            ", useCache=" + useCache +
            ", buildContext='" + buildContext + '\'' +
            '}';
    }
}
