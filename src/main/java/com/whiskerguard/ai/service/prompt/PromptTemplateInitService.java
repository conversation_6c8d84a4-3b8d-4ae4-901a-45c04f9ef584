package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.domain.enumeration.VariableType;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

/**
 * 提示词模板初始化服务
 * <p>
 * 在应用启动时初始化系统默认的提示词模板，
 * 将现有硬编码的提示词转换为模板化管理。
 */
@Service
@Transactional
public class PromptTemplateInitService implements CommandLineRunner {

    private final Logger log = LoggerFactory.getLogger(PromptTemplateInitService.class);

    private final PromptTemplateRepository promptTemplateRepository;

    public PromptTemplateInitService(PromptTemplateRepository promptTemplateRepository) {
        this.promptTemplateRepository = promptTemplateRepository;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化系统默认提示词模板");

        try {
            // 检查是否已经初始化过
            if (promptTemplateRepository.count() > 0) {
                log.info("提示词模板已存在，跳过初始化");
                return;
            }

            // 初始化各种类型的模板
            initContractReviewTemplates();
            initPolicyReviewTemplates();
            initGeneralTemplates();

            log.info("系统默认提示词模板初始化完成");

        } catch (Exception e) {
            log.error("初始化提示词模板失败", e);
        }
    }

    /**
     * 初始化合同审查相关模板
     */
    private void initContractReviewTemplates() {
        log.info("初始化合同审查模板");

        // 1. 合同综合审查模板
        PromptTemplate contractReviewTemplate = createTemplate(
            "CONTRACT_COMPREHENSIVE_REVIEW",
            "合同综合审查模板",
            "用于合同全面审查的标准模板，包含法律合规、风险评估、条款分析等功能",
            PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW,
            buildContractReviewContent()
        );

        // 添加变量
        contractReviewTemplate.addPromptTemplateVariable(createVariable(
            "CONTRACT_CONTENT", "合同内容", "待审查的合同完整内容",
            VariableType.BUSINESS, true, null, "合同的完整文本内容", 1
        ));

        contractReviewTemplate.addPromptTemplateVariable(createVariable(
            "CONTRACT_TYPE", "合同类型", "合同的业务类型",
            VariableType.BUSINESS, false, "通用合同", "如：采购合同、销售合同、服务合同等", 2
        ));

        contractReviewTemplate.addPromptTemplateVariable(createVariable(
            "COMPANY_INFO", "企业信息", "委托审查的企业基本信息",
            VariableType.BUSINESS, false, null, "企业名称、行业、规模等信息", 3
        ));

        contractReviewTemplate.addPromptTemplateVariable(createVariable(
            "REVIEW_FOCUS", "审查重点", "本次审查的重点关注领域",
            VariableType.BUSINESS, false, "全面审查", "如：法律合规、风险评估、财务条款等", 4
        ));

        promptTemplateRepository.save(contractReviewTemplate);

        // 2. 合同风险评估模板
        PromptTemplate riskAssessmentTemplate = createTemplate(
            "CONTRACT_RISK_ASSESSMENT",
            "合同风险评估模板",
            "专门用于合同风险识别和评估的模板",
            PromptTemplateType.CONTRACT_RISK_ASSESSMENT,
            buildContractRiskAssessmentContent()
        );

        // 添加变量
        riskAssessmentTemplate.addPromptTemplateVariable(createVariable(
            "CONTRACT_CONTENT", "合同内容", "待评估的合同内容",
            VariableType.BUSINESS, true, null, "合同的完整文本内容", 1
        ));

        riskAssessmentTemplate.addPromptTemplateVariable(createVariable(
            "RISK_TOLERANCE", "风险容忍度", "企业的风险承受能力",
            VariableType.BUSINESS, false, "中等", "高、中、低", 2
        ));

        promptTemplateRepository.save(riskAssessmentTemplate);
    }

    /**
     * 初始化制度审查相关模板
     */
    private void initPolicyReviewTemplates() {
        log.info("初始化制度审查模板");

        // 1. 法规转制度模板
        PromptTemplate regulatoryConversionTemplate = createTemplate(
            "POLICY_REGULATORY_CONVERSION",
            "法规转制度模板",
            "将法规条文转换为企业内部管理制度的模板",
            PromptTemplateType.POLICY_REGULATORY_CONVERSION,
            buildRegulatoryConversionContent()
        );

        // 添加变量
        regulatoryConversionTemplate.addPromptTemplateVariable(createVariable(
            "LEGAL_TEXT", "法规内容", "需要转换的法规条文内容",
            VariableType.BUSINESS, true, null, "完整的法规条文", 1
        ));

        regulatoryConversionTemplate.addPromptTemplateVariable(createVariable(
            "COMPANY_NAME", "企业名称", "制定制度的企业名称",
            VariableType.BUSINESS, true, null, "企业的正式名称", 2
        ));

        regulatoryConversionTemplate.addPromptTemplateVariable(createVariable(
            "INDUSTRY", "行业类型", "企业所属行业",
            VariableType.BUSINESS, false, null, "如：金融、制造、科技等", 3
        ));

        regulatoryConversionTemplate.addPromptTemplateVariable(createVariable(
            "COMPANY_SIZE", "企业规模", "企业的规模大小",
            VariableType.BUSINESS, false, "中型企业", "大型、中型、小型企业", 4
        ));

        promptTemplateRepository.save(regulatoryConversionTemplate);

        // 2. 内部制度审查模板
        PromptTemplate internalPolicyTemplate = createTemplate(
            "POLICY_INTERNAL_REVIEW",
            "内部制度审查模板",
            "用于审查企业内部制度的合规性和完整性",
            PromptTemplateType.POLICY_INTERNAL_REVIEW,
            buildInternalPolicyReviewContent()
        );

        // 添加变量
        internalPolicyTemplate.addPromptTemplateVariable(createVariable(
            "POLICY_CONTENT", "制度内容", "待审查的内部制度内容",
            VariableType.BUSINESS, true, null, "完整的制度文本", 1
        ));

        internalPolicyTemplate.addPromptTemplateVariable(createVariable(
            "POLICY_TYPE", "制度类型", "制度的分类类型",
            VariableType.BUSINESS, false, null, "如：人事制度、财务制度、安全制度等", 2
        ));

        internalPolicyTemplate.addPromptTemplateVariable(createVariable(
            "DEPARTMENT", "制定部门", "制度的制定部门",
            VariableType.BUSINESS, false, null, "负责制定该制度的部门", 3
        ));

        promptTemplateRepository.save(internalPolicyTemplate);
    }

    /**
     * 初始化通用模板
     */
    private void initGeneralTemplates() {
        log.info("初始化通用模板");

        // 通用分析模板
        PromptTemplate generalAnalysisTemplate = createTemplate(
            "GENERAL_ANALYSIS",
            "通用分析模板",
            "用于通用文本分析的基础模板",
            PromptTemplateType.GENERAL_ANALYSIS,
            buildGeneralAnalysisContent()
        );

        generalAnalysisTemplate.addPromptTemplateVariable(createVariable(
            "ANALYSIS_CONTENT", "分析内容", "需要分析的文本内容",
            VariableType.BUSINESS, true, null, "待分析的文本", 1
        ));

        generalAnalysisTemplate.addPromptTemplateVariable(createVariable(
            "ANALYSIS_TYPE", "分析类型", "分析的具体类型",
            VariableType.BUSINESS, false, "综合分析", "如：风险分析、合规分析、内容分析等", 2
        ));

        promptTemplateRepository.save(generalAnalysisTemplate);
    }

    /**
     * 创建模板对象
     */
    private PromptTemplate createTemplate(String templateKey, String name, String description, 
                                        PromptTemplateType templateType, String content) {
        PromptTemplate template = new PromptTemplate();
        template.setTemplateKey(templateKey);
        template.setName(name);
        template.setDescription(description);
        template.setTemplateType(templateType);
        template.setContent(content);
        template.setStatus(PromptTemplateStatus.PUBLISHED);
        template.setTemplateVersion(1);
        template.setIsSystemDefault(true);
        template.setTenantId(null); // 系统默认模板
        template.setUsageCount(0L);
        template.setCreatedBy("system");
        template.setCreatedAt(Instant.now());
        template.setUpdatedBy("system");
        template.setUpdatedAt(Instant.now());
        template.setIsDeleted(false);
        template.setVersion(1);
        template.setPromptTemplateVariables(new HashSet<>());
        return template;
    }

    /**
     * 创建变量对象
     */
    private PromptTemplateVariable createVariable(String variableName, String displayName, String description,
                                                VariableType variableType, boolean isRequired, String defaultValue,
                                                String exampleValue, int sortOrder) {
        PromptTemplateVariable variable = new PromptTemplateVariable();
        variable.setTenantId(1L); // 系统默认租户ID
        variable.setVariableName(variableName);
        variable.setDisplayName(displayName);
        variable.setDescription(description);
        variable.setVariableType(variableType);
        variable.setIsRequired(isRequired);
        variable.setDefaultValue(defaultValue);
        variable.setExampleValue(exampleValue);
        variable.setSortOrder(sortOrder);
        variable.setIsEnabled(true);
        variable.setCreatedBy("system");
        variable.setCreatedAt(Instant.now());
        variable.setUpdatedBy("system");
        variable.setUpdatedAt(Instant.now());
        variable.setIsDeleted(false);
        variable.setVersion(1);
        return variable;
    }

    // 模板内容构建方法

    private String buildContractReviewContent() {
        return """
            # 合同智能审查任务
            
            请对以下合同进行全面的智能审查分析：
            
            ## 合同基本信息
            - 合同类型：{{CONTRACT_TYPE}}
            - 合同内容：{{CONTRACT_CONTENT}}
            
            ## 企业信息
            {{COMPANY_INFO}}
            
            ## 审查要求
            请从以下维度进行分析：
            1. **法律合规性分析** - 检查合同条款是否符合相关法律法规
            2. **风险识别与评估** - 识别潜在的法律风险和商业风险
            3. **条款完整性检查** - 评估合同条款的完整性和合理性
            4. **财务条款审查** - 分析付款条件、违约责任等财务相关条款
            
            ## 审查重点
            {{REVIEW_FOCUS}}
            
            ## 输出要求
            请严格按照JSON格式返回结果，包含：
            - 整体风险等级评估
            - 具体风险点列表
            - 条款问题分析
            - 修改建议
            - 法律依据说明
            """;
    }

    private String buildContractRiskAssessmentContent() {
        return """
            # 合同风险评估任务
            
            请对以下合同进行专业的风险评估：
            
            ## 合同内容
            {{CONTRACT_CONTENT}}
            
            ## 风险评估参数
            - 风险容忍度：{{RISK_TOLERANCE}}
            
            ## 评估维度
            1. **法律合规风险** - 评估违反法律法规的风险
            2. **商业风险** - 评估商业运营相关风险
            3. **财务风险** - 评估财务损失风险
            4. **操作风险** - 评估执行过程中的风险
            5. **声誉风险** - 评估对企业声誉的影响
            
            ## 输出要求
            请提供详细的风险评估报告，包含风险等级、影响程度、发生概率和应对建议。
            """;
    }

    private String buildRegulatoryConversionContent() {
        return """
            # 法规到内部管理制度转换任务
            
            请根据以下法规内容，为企业制定相应的内部管理制度：
            
            ## 法规内容
            {{LEGAL_TEXT}}
            
            ## 企业信息
            - 企业名称：{{COMPANY_NAME}}
            - 行业类型：{{INDUSTRY}}
            - 企业规模：{{COMPANY_SIZE}}
            
            ## 转换要求
            1. **结合企业实际情况** - 根据企业规模和行业特点调整制度内容
            2. **确保制度的可操作性** - 制度条款应具体明确，便于执行
            3. **体现法规的核心要求** - 确保制度符合法规的基本要求
            4. **考虑企业的管理特点** - 结合企业现有管理体系
            
            ## 输出要求
            请生成完整的内部管理制度文档，包含制度目的、适用范围、具体条款、执行程序等。
            """;
    }

    private String buildInternalPolicyReviewContent() {
        return """
            # 内部制度智能审查任务
            
            请对以下内部制度进行全面的智能审查分析：
            
            ## 制度基本信息
            - 制度类型：{{POLICY_TYPE}}
            - 制定部门：{{DEPARTMENT}}
            
            ## 制度内容
            {{POLICY_CONTENT}}
            
            ## 审查维度
            1. **合规性检查** - 检查制度是否符合相关法律法规
            2. **可操作性分析** - 评估制度条款的可执行性
            3. **风险识别** - 识别制度执行中的潜在风险
            4. **完整性评估** - 评估制度内容的完整性和逻辑性
            
            ## 输出要求
            请提供详细的审查报告和改进建议，确保制度的合规性和有效性。
            """;
    }

    private String buildGeneralAnalysisContent() {
        return """
            # 通用文本分析任务
            
            请对以下内容进行{{ANALYSIS_TYPE}}：
            
            ## 分析内容
            {{ANALYSIS_CONTENT}}
            
            ## 分析要求
            请根据内容特点进行深入分析，提供专业的见解和建议。
            
            ## 输出要求
            请提供结构化的分析结果，包含关键发现、风险点、建议等。
            """;
    }
}
