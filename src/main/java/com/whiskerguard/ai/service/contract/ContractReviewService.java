/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewService.java
 * 包    名：com.whiskerguard.ai.service.contract
 * 描    述：合同智能审查核心服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.*;
import com.whiskerguard.ai.domain.ContractReview;
import com.whiskerguard.ai.domain.enumeration.ReviewStatus;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.repository.ContractReviewRepository;
import com.whiskerguard.ai.service.contract.context.*;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.web.rest.ContractReviewController;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同智能审查核心服务
 * <p>
 * 提供完整的合同智能审查功能，包括：
 * 1. 多租户数据隔离
 * 2. 多维度信息检索和整合
 * 3. AI智能分析
 * 4. 结构化结果输出
 */
@Service
@Transactional
public class ContractReviewService {

    private final Logger log = LoggerFactory.getLogger(ContractReviewService.class);

    private final RetrievalServiceClient retrievalServiceClient;
    private final GeneralServiceClient generalServiceClient;
    private final RegulatoryServiceClient regulatoryServiceClient;
    private final AiInvocationService aiInvocationService;
    private final ContractPartyExtractor partyExtractor;
    private final ContractReviewPromptBuilder promptBuilder;
    private final ContractReviewRepository reviewRepository;
    private final ObjectMapper objectMapper;

    public ContractReviewService(
        RetrievalServiceClient retrievalServiceClient,
        GeneralServiceClient generalServiceClient,
        RegulatoryServiceClient regulatoryServiceClient,
        AiInvocationService aiInvocationService,
        ContractPartyExtractor partyExtractor,
        ContractReviewPromptBuilder promptBuilder,
        ContractReviewRepository reviewRepository,
        ObjectMapper objectMapper
    ) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.generalServiceClient = generalServiceClient;
        this.regulatoryServiceClient = regulatoryServiceClient;
        this.aiInvocationService = aiInvocationService;
        this.partyExtractor = partyExtractor;
        this.promptBuilder = promptBuilder;
        this.reviewRepository = reviewRepository;
        this.objectMapper = objectMapper;
    }

    /**
     * 合同智能审查主流程（多租户隔离）
     */
    public ContractReviewResponseDTO reviewContract(ContractReviewRequestDTO request) {
        long startTime = System.currentTimeMillis();

        // 多租户数据隔离验证
        validateTenantAccess(request.getTenantId(), request.getEmployeeId());

        log.info(
            "开始合同智能审查 - 租户ID: {}, 员工ID: {}, 合同类型: {}",
            request.getTenantId(),
            request.getEmployeeId(),
            request.getContractType()
        );

        // 创建审查记录（多租户隔离）
        ContractReview reviewRecord = createReviewRecord(request);
        reviewRecord = reviewRepository.save(reviewRecord);

        try {
            // 1. 提取合同关联方
            List<ContractPartyExtractor.ContractPartyInfo> parties = partyExtractor.extractParties(request.getContractContent());
            log.debug("租户 {} 提取到关联方数量: {}", request.getTenantId(), parties.size());

            // 2. 并行获取多维度信息（所有调用都带租户ID）
            CompletableFuture<EnterpriseInfoContext> enterpriseInfoFuture = CompletableFuture.supplyAsync(() ->
                getEnterpriseInfo(parties, request.getTenantId())
            );

            CompletableFuture<LegalRegulatoryContext> legalContextFuture = CompletableFuture.supplyAsync(() ->
                getLegalRegulatoryContext(request, parties)
            );

            CompletableFuture<InternalPolicyContext> internalPolicyFuture = CompletableFuture.supplyAsync(() ->
                getInternalPolicyContext(request.getTenantId(), request.getContractType())
            );

            CompletableFuture<HistoricalCaseContext> historicalCaseFuture = CompletableFuture.supplyAsync(() ->
                getHistoricalCaseContext(request)
            );

            // 3. 等待所有并行任务完成
            EnterpriseInfoContext enterpriseInfo = enterpriseInfoFuture.get(120, TimeUnit.SECONDS);
            LegalRegulatoryContext legalContext = legalContextFuture.get(120, TimeUnit.SECONDS);
            InternalPolicyContext internalPolicy = internalPolicyFuture.get(120, TimeUnit.SECONDS);
            HistoricalCaseContext historicalCase = historicalCaseFuture.get(120, TimeUnit.SECONDS);

            // 4. 构建多维度增强提示词
            String enhancedPrompt = promptBuilder.buildComprehensiveReviewPrompt(
                request.getContractContent(),
                request.getContractType(),
                parties,
                enterpriseInfo,
                legalContext,
                internalPolicy,
                historicalCase,
                request
            );

            // 5. 调用LLM进行智能分析（带租户隔离）
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO(
                "deepseek", // 综合审查工具
                enhancedPrompt,
                buildMetadata(request, reviewRecord.getId()),
                request.getTenantId(),
                request.getEmployeeId()
            );

            AiRequestDTO aiResponse = aiInvocationService.invoke(aiRequest);

            // 6. 解析并结构化结果
            ContractReviewResponseDTO response = parseAndStructureResult(
                aiResponse.getResponse(),
                parties,
                enterpriseInfo,
                legalContext,
                internalPolicy,
                request.getTenantId()
            );

            // 7. 更新审查记录
            long duration = System.currentTimeMillis() - startTime;
            updateReviewRecord(reviewRecord, response, aiResponse, duration);
            response.setReviewId(reviewRecord.getId());
            response.setReviewTime(Instant.now());
            response.setReviewDuration(duration);

            log.info(
                "租户 {} 合同智能审查完成，风险等级: {}, 审查ID: {}, 耗时: {}ms",
                request.getTenantId(),
                response.getOverallRiskLevel(),
                reviewRecord.getId(),
                duration
            );

            return response;
        } catch (Exception e) {
            log.error("租户 {} 合同智能审查失败", request.getTenantId(), e);
            updateReviewRecordWithError(reviewRecord, e);
            throw new RuntimeException("合同审查处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取企业信息（天眼查 + RAG）
     */
    private EnterpriseInfoContext getEnterpriseInfo(List<ContractPartyExtractor.ContractPartyInfo> parties, Long tenantId) {
        EnterpriseInfoContext context = new EnterpriseInfoContext();

        for (ContractPartyExtractor.ContractPartyInfo party : parties) {
            if (party.getType() == com.whiskerguard.ai.domain.enumeration.PartyType.COMPANY) {
                try {
                    // 1. 从天眼查获取实时企业信息
                    CompanyBasicInfoDTO basicInfo = generalServiceClient.getCompanyBasicInfo(party.getName(), tenantId);
                    CompanyRiskInfoDTO riskInfo = generalServiceClient.getCompanyRiskInfo(party.getName(), tenantId);
                    CompanyLawsuitInfoDTO lawsuitInfo = generalServiceClient.getCompanyLawsuitInfo(party.getName(), tenantId);
                    CompanyCreditInfoDTO creditInfo = generalServiceClient.getCompanyCreditInfo(party.getName(), tenantId);

                    // 2. 从RAG检索历史风险案例和行业信息
                    String ragQuery = String.format(
                        "企业：%s 行业：%s 风险案例 合规问题 监管处罚",
                        party.getName(),
                        basicInfo != null ? basicInfo.getIndustry() : ""
                    );
                    RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 3, "cosine");
                    RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(tenantId.toString(), ragRequest);

                    // 3. 整合信息
                    EnterpriseInfoContext.EnterpriseInfo enterpriseInfo = new EnterpriseInfoContext.EnterpriseInfo(
                        party.getName(),
                        basicInfo,
                        riskInfo,
                        lawsuitInfo,
                        creditInfo,
                        ragResponse.getResults()
                    );

                    context.addEnterpriseInfo(enterpriseInfo);
                } catch (Exception e) {
                    log.warn("获取企业信息失败: {}, 错误: {}", party.getName(), e.getMessage());
                    // 降级处理：仅使用RAG检索
                    context.addEnterpriseInfo(getEnterpriseInfoFromRAGOnly(party.getName(), tenantId));
                }
            }
        }

        return context;
    }

    /**
     * RAG降级处理：仅从RAG获取企业信息
     */
    private EnterpriseInfoContext.EnterpriseInfo getEnterpriseInfoFromRAGOnly(String companyName, Long tenantId) {
        try {
            String ragQuery = String.format("企业：%s 基本信息 风险评估", companyName);
            RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 5, "cosine");
            RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(tenantId.toString(), ragRequest);

            return new EnterpriseInfoContext.EnterpriseInfo(companyName, null, null, null, null, ragResponse.getResults());
        } catch (Exception e) {
            log.warn("RAG降级处理也失败: {}", e.getMessage());
            return new EnterpriseInfoContext.EnterpriseInfo(companyName, null, null, null, null, new ArrayList<>());
        }
    }

    /**
     * 多租户访问权限验证
     */
    private void validateTenantAccess(Long tenantId, Long employeeId) {
        if (tenantId == null || employeeId == null) {
            throw new IllegalArgumentException("租户ID和员工ID不能为空");
        }

        // TODO: 实现具体的租户权限验证逻辑
        // 可以调用用户服务或从JWT token中验证
        log.debug("验证租户 {} 员工 {} 访问权限", tenantId, employeeId);
    }

    /**
     * 创建审查记录（多租户隔离）
     */
    private ContractReview createReviewRecord(ContractReviewRequestDTO request) {
        ContractReview record = new ContractReview();
        record.setTenantId(request.getTenantId()); // 租户隔离
        record.setEmployeeId(request.getEmployeeId());
        record.setContractType(request.getContractType());
        record.setContractTitle(request.getContractTitle());
        record.setContractContent(request.getContractContent());
        record.setStatus(ReviewStatus.PROCESSING);
        record.setReviewStartTime(Instant.now());
        record.setCreatedAt(Instant.now());
        record.setCreatedBy(request.getEmployeeId().toString());
        record.setVersion(1);
        record.setIsDeleted(false);

        return record;
    }

    /**
     * 获取法律法规上下文（多租户 + 法规服务）
     */
    private LegalRegulatoryContext getLegalRegulatoryContext(
        ContractReviewRequestDTO request,
        List<ContractPartyExtractor.ContractPartyInfo> parties
    ) {
        try {
            LegalRegulatoryContext context = new LegalRegulatoryContext();

            // 1. 从法规制度服务获取相关法律法规
            List<LegalRegulationDTO> contractTypeRegulations = regulatoryServiceClient.getRegulationsByContractType(
                request.getContractType(),
                request.getTenantId()
            );
            context.setContractTypeRegulations(contractTypeRegulations);

            // 2. 根据企业所属行业获取行业法规
            Set<String> industries = extractIndustriesFromParties(parties);
            for (String industry : industries) {
                List<LegalRegulationDTO> industryRegulations = regulatoryServiceClient.getRegulationsByIndustry(
                    industry,
                    request.getTenantId()
                );
                context.addIndustryRegulations(industry, industryRegulations);
            }

            // 3. 获取合规检查清单
            ComplianceChecklistDTO checklist = regulatoryServiceClient.getComplianceChecklist(
                request.getTenantId(),
                request.getContractType(),
                industries.isEmpty() ? null : industries.iterator().next()
            );
            context.setComplianceChecklist(checklist);

            // 4. 从RAG检索补充法规解释和案例
            String ragQuery = String.format("合同类型：%s 法律法规解释 司法解释 典型案例", request.getContractType());
            RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 5, "cosine");
            RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), ragRequest);
            context.setRagSupplementaryInfo(ragResponse.getResults());

            log.debug("租户 {} 获取法律法规上下文完成，法规数量: {}", request.getTenantId(), contractTypeRegulations.size());

            return context;
        } catch (Exception e) {
            log.warn("租户 {} 获取法律法规上下文失败: {}", request.getTenantId(), e.getMessage());
            return new LegalRegulatoryContext(); // 返回空上下文，不影响主流程
        }
    }

    /**
     * 获取企业内部制度上下文（租户隔离）
     */
    private InternalPolicyContext getInternalPolicyContext(Long tenantId, String contractType) {
        try {
            // 获取租户的内部制度
            List<InternalPolicyDTO> allPolicies = regulatoryServiceClient.getInternalPolicies(tenantId, null);

            // 获取合同相关的内部制度
            List<InternalPolicyDTO> contractPolicies = regulatoryServiceClient.getInternalPolicies(tenantId, "CONTRACT");

            InternalPolicyContext context = new InternalPolicyContext(allPolicies, contractPolicies);

            log.debug("租户 {} 获取内部制度上下文完成，制度数量: {}", tenantId, allPolicies.size());
            return context;
        } catch (Exception e) {
            log.warn("租户 {} 获取内部制度上下文失败: {}", tenantId, e.getMessage());
            return new InternalPolicyContext();
        }
    }

    /**
     * 获取历史案例上下文（租户隔离的RAG检索）
     */
    private HistoricalCaseContext getHistoricalCaseContext(ContractReviewRequestDTO request) {
        try {
            // 1. 检索租户历史审查案例
            String tenantHistoryQuery = String.format("租户：%s 合同审查 历史案例 风险点", request.getTenantId());
            RetrieveRequestDTO tenantRequest = new RetrieveRequestDTO(tenantHistoryQuery, 3, "cosine");
            RetrieveResponseDTO tenantResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), tenantRequest);

            // 2. 检索行业通用案例
            String industryQuery = String.format("合同类型：%s 行业案例 风险分析 最佳实践", request.getContractType());
            RetrieveRequestDTO industryRequest = new RetrieveRequestDTO(industryQuery, 5, "cosine");
            RetrieveResponseDTO industryResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), industryRequest);

            return new HistoricalCaseContext(tenantResponse.getResults(), industryResponse.getResults());
        } catch (Exception e) {
            log.warn("租户 {} 获取历史案例上下文失败: {}", request.getTenantId(), e.getMessage());
            return new HistoricalCaseContext();
        }
    }

    /**
     * 从关联方中提取行业信息
     */
    private Set<String> extractIndustriesFromParties(List<ContractPartyExtractor.ContractPartyInfo> parties) {
        Set<String> industries = new HashSet<>();

        // 这里可以根据企业名称推断行业，或者从已获取的企业信息中提取
        // 简化实现：返回一些通用行业
        industries.add("制造业");
        industries.add("服务业");

        return industries;
    }

    /**
     * 构建AI调用元数据
     */
    private Map<String, Object> buildMetadata(ContractReviewRequestDTO request, Long reviewId) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("reviewId", reviewId);
        metadata.put("contractType", request.getContractType());
        metadata.put("tenantId", request.getTenantId());
        metadata.put("employeeId", request.getEmployeeId());
        metadata.put("priority", request.getPriority());
        metadata.put("reviewFocus", request.getReviewFocus());

        if (request.getMetadata() != null) {
            metadata.putAll(request.getMetadata());
        }

        return metadata;
    }

    /**
     * 解析并结构化AI返回结果
     */
    private ContractReviewResponseDTO parseAndStructureResult(
        String aiResponse,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        Long tenantId
    ) {
        try {
            // 清理AI返回的响应，移除可能存在的markdown代码块标记
            String cleanedResponse = cleanAiResponse(aiResponse);
            log.debug("清理后的AI响应内容: {}", cleanedResponse);

            // 尝试解析JSON结果
            Map<String, Object> resultMap = objectMapper.readValue(cleanedResponse, Map.class);
            log.debug("解析后的JSON Map: {}", resultMap);

            ContractReviewResponseDTO response = new ContractReviewResponseDTO();

            // 基本信息 - 支持中英文字段名
            String riskLevel = getStringValue(resultMap, "整体风险等级评估", "overallRiskLevel");
            response.setOverallRiskLevel(parseRiskLevel(riskLevel));

            Integer riskScore = getIntegerValue(resultMap, "风险评分", "riskScore");
            response.setRiskScore(riskScore);

            String riskSummary = getStringValue(resultMap, "风险总结", "riskSummary");
            response.setRiskSummary(riskSummary);

            // 风险点 - 支持中英文字段名
            List<Object> riskPointsListRaw = getListValue(resultMap, "具体风险点列表", "riskPoints");
            if (riskPointsListRaw != null && !riskPointsListRaw.isEmpty()) {
                List<ContractReviewResponseDTO.RiskPointDTO> riskPoints = new ArrayList<>();
                for (Object riskItem : riskPointsListRaw) {
                    ContractReviewResponseDTO.RiskPointDTO riskPoint = new ContractReviewResponseDTO.RiskPointDTO();
                    riskPoint.setCategory("合同条款风险");

                    // 安全地转换为字符串
                    String riskDescription = riskItem != null ? riskItem.toString() : "未知风险";
                    riskPoint.setDescription(riskDescription);
                    riskPoint.setSeverity(RiskLevel.MEDIUM); // 默认中等风险
                    riskPoint.setRiskScore(60); // 默认风险分数
                    riskPoint.setIsCritical(false);
                    riskPoints.add(riskPoint);
                }
                response.setRiskPoints(riskPoints);
            }

            // 如果有详细的风险点数据结构，也支持解析
            List<Map<String, Object>> detailedRiskPointsData = getListMapValue(resultMap, "详细风险点", "detailedRiskPoints");
            if (detailedRiskPointsData != null && !detailedRiskPointsData.isEmpty()) {
                List<ContractReviewResponseDTO.RiskPointDTO> riskPoints = new ArrayList<>();
                for (Map<String, Object> riskData : detailedRiskPointsData) {
                    ContractReviewResponseDTO.RiskPointDTO riskPoint = new ContractReviewResponseDTO.RiskPointDTO();
                    riskPoint.setCategory(getStringValue(riskData, "类别", "category"));
                    riskPoint.setDescription(getStringValue(riskData, "描述", "description"));
                    riskPoint.setSeverity(parseRiskLevel(getStringValue(riskData, "严重程度", "severity")));
                    riskPoint.setAffectedClauses(getListValue(riskData, "影响条款", "affectedClauses"));
                    riskPoint.setLegalBasis(getStringValue(riskData, "法律依据", "legalBasis"));
                    riskPoint.setSuggestions(getListValue(riskData, "建议", "suggestions"));
                    riskPoint.setRiskScore(getIntegerValue(riskData, "风险分数", "riskScore"));
                    riskPoint.setIsCritical(getBooleanValue(riskData, "是否关键", "isCritical"));
                    riskPoints.add(riskPoint);
                }
                response.setRiskPoints(riskPoints);
            }

            // 关联方分析
            List<Map<String, Object>> partyAnalysisData = (List<Map<String, Object>>) resultMap.get("partyAnalysis");
            if (partyAnalysisData != null) {
                List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> partyAnalysis = new ArrayList<>();
                for (Map<String, Object> partyData : partyAnalysisData) {
                    ContractReviewResponseDTO.PartyRiskAnalysisDTO partyRisk = new ContractReviewResponseDTO.PartyRiskAnalysisDTO();
                    partyRisk.setPartyName((String) partyData.get("partyName"));
                    partyRisk.setPartyType((String) partyData.get("partyType"));
                    partyRisk.setRiskLevel(parseRiskLevel((String) partyData.get("riskLevel")));
                    partyRisk.setRiskFactors((List<String>) partyData.get("riskFactors"));
                    partyRisk.setComplianceIssues((List<String>) partyData.get("complianceIssues"));
                    partyRisk.setRecommendations((List<String>) partyData.get("recommendations"));
                    partyRisk.setCreditRating((String) partyData.get("creditRating"));
                    partyRisk.setBusinessStatus((String) partyData.get("businessStatus"));
                    partyAnalysis.add(partyRisk);
                }
                response.setPartyAnalysis(partyAnalysis);
            }

            // 条款问题分析 - 支持中英文字段名
            Map<String, Object> clauseAnalysisData = getMapValue(resultMap, "条款问题分析", "clauseAnalysis");
            if (clauseAnalysisData != null) {
                List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues = new ArrayList<>();

                // 遍历条款问题分析中的每个条款
                for (Map.Entry<String, Object> entry : clauseAnalysisData.entrySet()) {
                    String clauseName = entry.getKey();
                    Object clauseValue = entry.getValue();

                    if (clauseValue instanceof Map) {
                        Map<String, Object> clauseDetails = (Map<String, Object>) clauseValue;
                        ContractReviewResponseDTO.ClauseIssueDTO clauseIssue = new ContractReviewResponseDTO.ClauseIssueDTO();

                        clauseIssue.setClauseText(clauseName);
                        clauseIssue.setClauseNumber(clauseName);
                        clauseIssue.setIssueType("条款问题");
                        clauseIssue.setDescription(getStringValue(clauseDetails, "问题", "problem"));
                        clauseIssue.setSeverity(RiskLevel.MEDIUM);
                        clauseIssue.setLegalRisk(getStringValue(clauseDetails, "风险", "risk"));
                        clauseIssue.setReferenceLaws(List.of(getStringValue(clauseDetails, "法律依据", "legalBasis")));

                        clauseIssues.add(clauseIssue);
                    }
                }
                response.setClauseIssues(clauseIssues);
            }

            // 如果有标准格式的条款问题数据，也支持解析
            List<Map<String, Object>> standardClauseIssuesData = getListMapValue(resultMap, "条款问题", "clauseIssues");
            if (standardClauseIssuesData != null && !standardClauseIssuesData.isEmpty()) {
                List<ContractReviewResponseDTO.ClauseIssueDTO> clauseIssues = new ArrayList<>();
                for (Map<String, Object> clauseData : standardClauseIssuesData) {
                    ContractReviewResponseDTO.ClauseIssueDTO clauseIssue = new ContractReviewResponseDTO.ClauseIssueDTO();
                    clauseIssue.setClauseText(getStringValue(clauseData, "条款文本", "clauseText"));
                    clauseIssue.setClauseNumber(getStringValue(clauseData, "条款编号", "clauseNumber"));
                    clauseIssue.setIssueType(getStringValue(clauseData, "问题类型", "issueType"));
                    clauseIssue.setDescription(getStringValue(clauseData, "描述", "description"));
                    clauseIssue.setSeverity(parseRiskLevel(getStringValue(clauseData, "严重程度", "severity")));
                    clauseIssue.setLegalRisk(getStringValue(clauseData, "法律风险", "legalRisk"));
                    clauseIssue.setSuggestions(getListValue(clauseData, "建议", "suggestions"));
                    clauseIssue.setReferenceLaws(getListValue(clauseData, "参考法律", "referenceLaws"));
                    clauseIssues.add(clauseIssue);
                }
                response.setClauseIssues(clauseIssues);
            }

            // 合规检查结果
            Map<String, Object> complianceData = (Map<String, Object>) resultMap.get("complianceCheck");
            if (complianceData != null) {
                ContractReviewResponseDTO.ComplianceCheckResultDTO complianceCheck =
                    new ContractReviewResponseDTO.ComplianceCheckResultDTO();
                complianceCheck.setViolatedRegulations((List<String>) complianceData.get("violatedRegulations"));
                complianceCheck.setInternalPolicyViolations((List<String>) complianceData.get("internalPolicyViolations"));
                complianceCheck.setRecommendations((List<String>) complianceData.get("recommendations"));
                complianceCheck.setRequiredActions((List<String>) complianceData.get("requiredActions"));
                complianceCheck.setOverallCompliance((Boolean) complianceData.get("overallCompliance"));
                complianceCheck.setComplianceScore((String) complianceData.get("complianceScore"));
                response.setComplianceCheck(complianceCheck);
            }

            // 修改建议 - 支持中英文字段名
            List<String> recommendations = new ArrayList<>();

            // 处理修改建议列表
            List<Object> modificationSuggestions = getListValue(resultMap, "修改建议", "recommendations");
            if (modificationSuggestions != null) {
                for (Object suggestion : modificationSuggestions) {
                    if (suggestion instanceof Map) {
                        Map<String, Object> suggestionMap = (Map<String, Object>) suggestion;
                        String clause = getStringValue(suggestionMap, "条款", "clause");
                        String advice = getStringValue(suggestionMap, "建议", "suggestion");
                        recommendations.add(clause + ": " + advice);
                    } else if (suggestion instanceof String) {
                        recommendations.add((String) suggestion);
                    }
                }
            }

            // 如果没有修改建议，尝试获取标准建议
            if (recommendations.isEmpty()) {
                List<String> standardRecommendations = getListValue(resultMap, "建议", "recommendations");
                if (standardRecommendations != null) {
                    recommendations.addAll(standardRecommendations);
                }
            }

            response.setRecommendations(recommendations);

            // 下一步行动
            List<String> nextActions = getListValue(resultMap, "下一步行动", "nextActions");
            response.setNextActions(nextActions);

            // 设置其他信息
            response.setReviewStatus("COMPLETED");
            response.setAiModelInfo("contract-review-comprehensive");
            response.setConfidence(calculateConfidence(response));

            return response;
        } catch (Exception e) {
            log.error("解析AI返回结果失败，租户: {}", tenantId, e);
            // 返回默认结果
            return createDefaultResponse(aiResponse);
        }
    }

    /**
     * 清理AI返回的响应，移除markdown代码块标记
     *
     * @param aiResponse 原始AI响应
     * @return 清理后的JSON字符串
     */
    private String cleanAiResponse(String aiResponse) {
        if (aiResponse == null) {
            return null;
        }

        // 移除markdown代码块开始标记 (```json)
        String cleaned = aiResponse.replaceAll("```json", "");

        // 移除markdown代码块结束标记 (```)
        cleaned = cleaned.replaceAll("```", "");

        // 移除可能存在的前导和尾随空白字符
        cleaned = cleaned.trim();

        log.debug("清理后的AI响应: {}", cleaned);
        return cleaned;
    }

    /**
     * 解析风险等级
     */
    private RiskLevel parseRiskLevel(String riskLevelStr) {
        if (riskLevelStr == null) {
            return RiskLevel.MEDIUM;
        }

        try {
            return RiskLevel.valueOf(riskLevelStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("无法解析风险等级: {}", riskLevelStr);
            return RiskLevel.MEDIUM;
        }
    }

    /**
     * 计算置信度
     */
    private Integer calculateConfidence(ContractReviewResponseDTO response) {
        int confidence = 80; // 基础置信度

        // 根据风险点数量调整
        if (response.getRiskPoints() != null) {
            confidence += Math.min(response.getRiskPoints().size() * 2, 10);
        }

        // 根据关联方分析调整
        if (response.getPartyAnalysis() != null) {
            confidence += Math.min(response.getPartyAnalysis().size() * 3, 10);
        }

        return Math.min(confidence, 95);
    }

    /**
     * 创建默认响应（解析失败时使用）
     */
    private ContractReviewResponseDTO createDefaultResponse(String aiResponse) {
        ContractReviewResponseDTO response = new ContractReviewResponseDTO();
        response.setOverallRiskLevel(RiskLevel.MEDIUM);
        response.setRiskScore(50);
        response.setRiskSummary("AI分析结果解析失败，请人工审查");
        response.setReviewStatus("FAILED");
        response.setConfidence(30);

        // 将原始AI响应作为建议
        List<String> recommendations = new ArrayList<>();
        recommendations.add("AI原始响应：" + aiResponse);
        response.setRecommendations(recommendations);

        return response;
    }

    /**
     * 更新审查记录
     */
    private void updateReviewRecord(
        ContractReview reviewRecord,
        ContractReviewResponseDTO response,
        AiRequestDTO aiResponse,
        long duration
    ) {
        try {
            reviewRecord.setStatus(ReviewStatus.COMPLETED);
            reviewRecord.setOverallRiskLevel(response.getOverallRiskLevel());
            reviewRecord.setRiskScore(response.getRiskScore());
            reviewRecord.setRiskSummary(response.getRiskSummary());
            reviewRecord.setReviewResult(objectMapper.writeValueAsString(response));
            reviewRecord.setReviewEndTime(Instant.now());
            reviewRecord.setReviewDuration(duration);
            reviewRecord.setAiRequestId(aiResponse.getId());
            reviewRecord.setUpdatedAt(Instant.now());
            reviewRecord.setUpdatedBy(reviewRecord.getEmployeeId().toString());

            reviewRepository.save(reviewRecord);
        } catch (Exception e) {
            log.error("更新审查记录失败，记录ID: {}", reviewRecord.getId(), e);
        }
    }

    /**
     * 更新审查记录（错误情况）
     */
    private void updateReviewRecordWithError(ContractReview reviewRecord, Exception error) {
        try {
            reviewRecord.setStatus(ReviewStatus.FAILED);
            reviewRecord.setReviewEndTime(Instant.now());
            reviewRecord.setReviewDuration(System.currentTimeMillis() - reviewRecord.getReviewStartTime().toEpochMilli());

            // 记录错误信息
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", error.getMessage());
            errorInfo.put("errorType", error.getClass().getSimpleName());
            errorInfo.put("timestamp", Instant.now().toString());

            reviewRecord.setReviewResult(objectMapper.writeValueAsString(errorInfo));
            reviewRecord.setUpdatedAt(Instant.now());
            reviewRecord.setUpdatedBy(reviewRecord.getEmployeeId().toString());

            reviewRepository.save(reviewRecord);
        } catch (Exception e) {
            log.error("更新错误审查记录失败，记录ID: {}", reviewRecord.getId(), e);
        }
    }

    /**
     * 获取审查历史（分页）
     */
    public Page<ContractReviewController.ContractReviewHistoryDTO> getReviewHistory(Long tenantId, Long employeeId, Pageable pageable) {
        log.debug("查询审查历史 - 租户: {}, 员工: {}", tenantId, employeeId);

        // 这里需要实现具体的分页查询逻辑
        // 由于涉及到Repository的分页查询，暂时返回空页面
        return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }

    /**
     * 获取审查详情
     */
    public ContractReviewResponseDTO getReviewDetail(Long reviewId, Long tenantId) {
        log.debug("获取审查详情 - 审查ID: {}, 租户: {}", reviewId, tenantId);

        // 这里需要实现具体的查询逻辑
        // 暂时返回null，需要后续实现
        return null;
    }

    /**
     * 导出审查报告
     */
    public byte[] exportReviewReport(Long reviewId, Long tenantId, String format) {
        log.info("导出审查报告 - 审查ID: {}, 租户: {}, 格式: {}", reviewId, tenantId, format);

        // 这里需要实现具体的报告导出逻辑
        // 暂时返回空数组，需要后续实现
        return new byte[0];
    }

    /**
     * 获取审查统计信息
     */
    public Map<String, Object> getReviewStatistics(Long tenantId, Integer days) {
        log.debug("获取审查统计信息 - 租户: {}, 天数: {}", tenantId, days);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalReviews", 0);
        statistics.put("highRiskCount", 0);
        statistics.put("mediumRiskCount", 0);
        statistics.put("lowRiskCount", 0);
        statistics.put("averageRiskScore", 0);
        statistics.put("averageReviewTime", 0);

        // 这里需要实现具体的统计逻辑
        // 暂时返回默认值，需要后续实现
        return statistics;
    }

    /**
     * 获取字符串值 - 支持中英文字段名
     */
    private String getStringValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        return value != null ? value.toString() : null;
    }

    /**
     * 获取整数值 - 支持中英文字段名
     */
    private Integer getIntegerValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析整数值: {}", value);
                return null;
            }
        }
        return null;
    }

    /**
     * 获取布尔值 - 支持中英文字段名
     */
    private Boolean getBooleanValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }

    /**
     * 获取列表值 - 支持中英文字段名
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> getListValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        if (value instanceof List) {
            return (List<T>) value;
        }
        return null;
    }

    /**
     * 获取Map值 - 支持中英文字段名
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getMapValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return null;
    }

    /**
     * 获取Map列表值 - 支持中英文字段名
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getListMapValue(Map<String, Object> map, String chineseKey, String englishKey) {
        Object value = map.get(chineseKey);
        if (value == null) {
            value = map.get(englishKey);
        }
        if (value instanceof List) {
            return (List<Map<String, Object>>) value;
        }
        return null;
    }
}
