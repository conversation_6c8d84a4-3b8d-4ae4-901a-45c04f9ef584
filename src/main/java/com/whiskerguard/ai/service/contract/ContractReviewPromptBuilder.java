/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewPromptBuilder.java
 * 包    名：com.whiskerguard.ai.service.contract
 * 描    述：合同审查提示词构建器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.contract;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.service.contract.context.EnterpriseInfoContext;
import com.whiskerguard.ai.service.contract.context.HistoricalCaseContext;
import com.whiskerguard.ai.service.contract.context.InternalPolicyContext;
import com.whiskerguard.ai.service.contract.context.LegalRegulatoryContext;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 合同审查提示词构建器
 * <p>
 * 负责将多维度的信息整合成结构化的提示词，
 * 指导LLM进行全面的合同智能审查。
 *
 * 重构说明：现在使用模板化的提示词构建服务，支持动态配置和租户个性化。
 */
@Component
public class ContractReviewPromptBuilder {

    private final Logger log = LoggerFactory.getLogger(ContractReviewPromptBuilder.class);

    private final PromptBuilderService promptBuilderService;

    public ContractReviewPromptBuilder(PromptBuilderService promptBuilderService) {
        this.promptBuilderService = promptBuilderService;
    }

    /**
     * 构建综合审查提示词（使用模板化方式）
     */
    public String buildComprehensiveReviewPrompt(
        String contractContent,
        String contractType,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        HistoricalCaseContext historicalCase,
        ContractReviewRequestDTO request
    ) {
        log.debug("开始构建合同审查提示词（模板化），租户ID: {}, 合同类型: {}", request.getTenantId(), contractType);

        try {
            // 1. 构建变量映射
            Map<String, Object> variables = buildVariableMap(
                contractContent, contractType, parties, enterpriseInfo,
                legalContext, internalPolicy, historicalCase, request
            );

            // 2. 使用模板构建服务
            PromptBuildRequest buildRequest = PromptBuildRequest.builder()
                .templateType(PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW)
                .tenantId(request.getTenantId())
                .variables(variables)
                .enableRagEnhancement(true) // 启用RAG增强
                .buildContext("合同综合审查")
                .build();

            String prompt = promptBuilderService.buildPrompt(buildRequest);

            log.debug("模板化提示词构建完成，租户ID: {}, 长度: {} 字符", request.getTenantId(), prompt.length());
            return prompt;

        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到传统方式 - 错误: {}", e.getMessage());
            // 回退到原有的硬编码方式
            return buildLegacyPrompt(contractContent, contractType, parties, enterpriseInfo,
                                   legalContext, internalPolicy, historicalCase, request);
        }
    }

    /**
     * 构建变量映射
     */
    private Map<String, Object> buildVariableMap(
        String contractContent,
        String contractType,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        HistoricalCaseContext historicalCase,
        ContractReviewRequestDTO request
    ) {
        Map<String, Object> variables = new HashMap<>();

        // 基础合同信息
        variables.put("CONTRACT_CONTENT", contractContent);
        variables.put("CONTRACT_TYPE", contractType);

        if (request.getContractTitle() != null) {
            variables.put("CONTRACT_TITLE", request.getContractTitle());
        }

        if (request.getContractAmount() != null) {
            variables.put("CONTRACT_AMOUNT", request.getContractAmount());
        }

        if (request.getContractTerm() != null) {
            variables.put("CONTRACT_TERM", request.getContractTerm());
        }

        // 企业和行业信息
        if (request.getIndustryCategory() != null) {
            variables.put("INDUSTRY_CATEGORY", request.getIndustryCategory());
        }

        if (request.getRiskTolerance() != null) {
            variables.put("RISK_TOLERANCE", request.getRiskTolerance());
        }

        // 审查配置
        if (request.getReviewFocus() != null) {
            variables.put("REVIEW_FOCUS", request.getReviewFocus().name());
        }

        if (request.getSpecialRequirements() != null) {
            variables.put("SPECIAL_REQUIREMENTS", request.getSpecialRequirements());
        }

        // 关联方信息
        if (parties != null && !parties.isEmpty()) {
            variables.put("CONTRACT_PARTIES", formatPartiesInfo(parties));
            variables.put("HAS_PARTIES", true);
        } else {
            variables.put("HAS_PARTIES", false);
        }

        // 企业详细信息
        if (enterpriseInfo != null && !enterpriseInfo.isEmpty()) {
            variables.put("ENTERPRISE_INFO", enterpriseInfo.formatForPrompt());
            variables.put("HAS_ENTERPRISE_INFO", true);
        } else {
            variables.put("HAS_ENTERPRISE_INFO", false);
        }

        // 法律法规信息
        if (legalContext != null && !legalContext.isEmpty()) {
            variables.put("LEGAL_REGULATIONS", legalContext.formatContractTypeRegulations());
            variables.put("INDUSTRY_REGULATIONS", legalContext.formatIndustryRegulations());
            variables.put("COMPLIANCE_CHECKLIST", legalContext.formatComplianceChecklist());
            variables.put("HAS_LEGAL_CONTEXT", true);
        } else {
            variables.put("HAS_LEGAL_CONTEXT", false);
        }

        // 内部制度信息
        if (internalPolicy != null && !internalPolicy.isEmpty()) {
            variables.put("INTERNAL_POLICIES", internalPolicy.formatForPrompt());
            variables.put("HAS_INTERNAL_POLICIES", true);
        } else {
            variables.put("HAS_INTERNAL_POLICIES", false);
        }

        // 历史案例信息
        if (historicalCase != null && !historicalCase.isEmpty()) {
            variables.put("HISTORICAL_CASES", historicalCase.formatTenantCases());
            variables.put("INDUSTRY_CASES", historicalCase.formatIndustryCases());
            variables.put("RISK_PATTERNS", String.join("; ", historicalCase.extractRiskPatterns()));
            variables.put("BEST_PRACTICES", String.join("; ", historicalCase.getBestPractices()));
            variables.put("HAS_HISTORICAL_CASES", true);
        } else {
            variables.put("HAS_HISTORICAL_CASES", false);
        }

        // 系统变量会由VariableResolver自动添加
        return variables;
    }

    /**
     * 格式化关联方信息
     */
    private String formatPartiesInfo(List<ContractPartyExtractor.ContractPartyInfo> parties) {
        StringBuilder sb = new StringBuilder();
        for (ContractPartyExtractor.ContractPartyInfo party : parties) {
            sb.append("- ").append(party.getName());
            sb.append("（").append(party.getType().name()).append("）");
            if (party.getRole() != null) {
                sb.append(" - ").append(party.getRole());
            }
            sb.append("\n");
            if (party.getContext() != null) {
                sb.append("  上下文：").append(party.getContext()).append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 回退到传统的硬编码提示词构建方式
     */
    private String buildLegacyPrompt(
        String contractContent,
        String contractType,
        List<ContractPartyExtractor.ContractPartyInfo> parties,
        EnterpriseInfoContext enterpriseInfo,
        LegalRegulatoryContext legalContext,
        InternalPolicyContext internalPolicy,
        HistoricalCaseContext historicalCase,
        ContractReviewRequestDTO request
    ) {
        log.debug("使用传统方式构建提示词");

        StringBuilder prompt = new StringBuilder();

        // 1. 系统角色定义
        prompt.append(buildSystemRole(request.getTenantId()));

        // 2. 合同基本信息
        prompt.append(buildContractInfo(contractContent, contractType, request));

        // 3. 关联方信息
        prompt.append(buildPartyInfo(parties));

        // 4. 企业详细信息
        if (!enterpriseInfo.isEmpty()) {
            prompt.append(buildEnterpriseInfo(enterpriseInfo));
        }

        // 5. 法律法规依据
        if (!legalContext.isEmpty()) {
            prompt.append(buildLegalRegulatoryInfo(legalContext));
        }

        // 6. 企业内部制度
        if (!internalPolicy.isEmpty()) {
            prompt.append(buildInternalPolicyInfo(internalPolicy));
        }

        // 7. 历史案例参考
        if (!historicalCase.isEmpty()) {
            prompt.append(buildHistoricalCaseInfo(historicalCase));
        }

        // 8. 审查要求和输出格式
        prompt.append(buildReviewRequirements(request));

        // 9. JSON输出模板
        prompt.append(buildJsonTemplate());

        String finalPrompt = prompt.toString();
        log.debug("传统提示词构建完成，总长度: {} 字符", finalPrompt.length());

        return finalPrompt;
    }

    /**
     * 构建系统角色定义
     */
    private String buildSystemRole(Long tenantId) {
        return """
        你是一位资深的合同审查专家和风险评估师，具有丰富的法律知识和实务经验。
        你正在为租户ID %d 提供专业的合同智能审查服务。

        你的专业能力包括：
        - 深度的法律法规理解和应用
        - 敏锐的商业风险识别能力
        - 丰富的合同审查实务经验
        - 精准的条款问题分析能力
        - 实用的合规建议提供能力

        请基于以下信息进行全面、专业、准确的合同审查分析。

        """.formatted(tenantId);
    }

    /**
     * 构建合同基本信息
     */
    private String buildContractInfo(String contractContent, String contractType, ContractReviewRequestDTO request) {
        StringBuilder sb = new StringBuilder();

        sb.append("【合同基本信息】\n");
        sb.append("合同类型：").append(contractType).append("\n");

        if (request.getContractTitle() != null) {
            sb.append("合同标题：").append(request.getContractTitle()).append("\n");
        }

        if (request.getContractAmount() != null) {
            sb.append("合同金额：").append(request.getContractAmount()).append("\n");
        }

        if (request.getContractTerm() != null) {
            sb.append("合同期限：").append(request.getContractTerm()).append("\n");
        }

        if (request.getIndustryCategory() != null) {
            sb.append("行业类别：").append(request.getIndustryCategory()).append("\n");
        }

        if (request.getRiskTolerance() != null) {
            sb.append("风险容忍度：").append(request.getRiskTolerance()).append("\n");
        }

        sb.append("\n合同内容：\n").append(contractContent).append("\n\n");

        return sb.toString();
    }

    /**
     * 构建关联方信息
     */
    private String buildPartyInfo(List<ContractPartyExtractor.ContractPartyInfo> parties) {
        StringBuilder sb = new StringBuilder();

        sb.append("【合同关联方】\n");
        if (parties.isEmpty()) {
            sb.append("未识别到明确的关联方信息\n\n");
            return sb.toString();
        }

        for (ContractPartyExtractor.ContractPartyInfo party : parties) {
            sb.append("- ").append(party.getName());
            sb.append("（").append(party.getType().name()).append("）");

            if (party.getRole() != null) {
                sb.append(" - ").append(party.getRole());
            }

            sb.append("\n");

            if (party.getContext() != null) {
                sb.append("  上下文：").append(party.getContext()).append("\n");
            }
        }

        sb.append("\n");
        return sb.toString();
    }

    /**
     * 构建企业详细信息
     */
    private String buildEnterpriseInfo(EnterpriseInfoContext enterpriseInfo) {
        StringBuilder sb = new StringBuilder();

        sb.append("【企业详细信息】\n");
        sb.append("以下是通过天眼查等权威渠道获取的企业真实信息：\n\n");
        sb.append(enterpriseInfo.formatForPrompt()).append("\n\n");

        return sb.toString();
    }

    /**
     * 构建法律法规信息
     */
    private String buildLegalRegulatoryInfo(LegalRegulatoryContext legalContext) {
        StringBuilder sb = new StringBuilder();

        sb.append("【适用法律法规】\n");

        // 合同类型相关法规
        sb.append("**合同类型相关法规：**\n");
        sb.append(legalContext.formatContractTypeRegulations()).append("\n\n");

        // 行业专门法规
        sb.append("**行业专门法规：**\n");
        sb.append(legalContext.formatIndustryRegulations()).append("\n\n");

        // 合规检查要点
        sb.append("**合规检查要点：**\n");
        sb.append(legalContext.formatComplianceChecklist()).append("\n\n");

        // RAG补充信息
        sb.append("**相关法规解释：**\n");
        sb.append(legalContext.formatRagSupplementaryInfo()).append("\n\n");

        return sb.toString();
    }

    /**
     * 构建企业内部制度信息
     */
    private String buildInternalPolicyInfo(InternalPolicyContext internalPolicy) {
        StringBuilder sb = new StringBuilder();

        sb.append("【企业内部制度要求】\n");
        sb.append("以下是该企业的内部制度要求，合同必须符合这些内部规定：\n\n");
        sb.append(internalPolicy.formatForPrompt()).append("\n\n");

        return sb.toString();
    }

    /**
     * 构建历史案例信息
     */
    private String buildHistoricalCaseInfo(HistoricalCaseContext historicalCase) {
        StringBuilder sb = new StringBuilder();

        sb.append("【历史案例参考】\n");

        // 租户历史案例
        sb.append(historicalCase.formatTenantCases()).append("\n\n");

        // 行业通用案例
        sb.append(historicalCase.formatIndustryCases()).append("\n\n");

        // 风险模式提取
        List<String> riskPatterns = historicalCase.extractRiskPatterns();
        if (!riskPatterns.isEmpty()) {
            sb.append("**常见风险模式：**\n");
            for (String pattern : riskPatterns) {
                sb.append("- ").append(pattern).append("\n");
            }
            sb.append("\n");
        }

        // 最佳实践
        List<String> bestPractices = historicalCase.getBestPractices();
        if (!bestPractices.isEmpty()) {
            sb.append("**最佳实践建议：**\n");
            for (String practice : bestPractices) {
                sb.append("- ").append(practice).append("\n");
            }
            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * 构建审查要求
     */
    private String buildReviewRequirements(ContractReviewRequestDTO request) {
        StringBuilder sb = new StringBuilder();

        sb.append("【审查要求】\n");
        sb.append("请基于以上信息进行全面的合同智能审查，特别注意：\n\n");

        sb.append("1. **法律合规性检查**\n");
        sb.append("   - 严格按照适用的法律法规进行合规性检查\n");
        sb.append("   - 识别可能违反的法律条文和监管要求\n");
        sb.append("   - 评估违规风险和潜在后果\n\n");

        sb.append("2. **企业内部制度合规**\n");
        sb.append("   - 结合企业内部制度要求进行审查\n");
        sb.append("   - 检查是否符合企业内部政策和流程\n");
        sb.append("   - 识别内部制度冲突和合规风险\n\n");

        sb.append("3. **关联方风险评估**\n");
        sb.append("   - 基于真实的企业信息评估商业风险\n");
        sb.append("   - 分析关联方的信用状况和经营风险\n");
        sb.append("   - 评估合作风险和履约能力\n\n");

        sb.append("4. **历史案例借鉴**\n");
        sb.append("   - 参考历史案例识别潜在风险\n");
        sb.append("   - 借鉴最佳实践提供改进建议\n");
        sb.append("   - 避免重复历史风险问题\n\n");

        // 根据审查重点调整要求
        if (request.getReviewFocus() != null) {
            sb.append("5. **审查重点**\n");
            switch (request.getReviewFocus()) {
                case LEGAL_ONLY:
                    sb.append("   - 本次审查重点关注法律合规性\n");
                    break;
                case RISK_ONLY:
                    sb.append("   - 本次审查重点关注风险评估\n");
                    break;
                case FINANCIAL_ONLY:
                    sb.append("   - 本次审查重点关注财务条款\n");
                    break;
                case OPERATIONAL_ONLY:
                    sb.append("   - 本次审查重点关注操作条款\n");
                    break;
                default:
                    sb.append("   - 进行全面综合审查\n");
            }
            sb.append("\n");
        }

        if (request.getSpecialRequirements() != null) {
            sb.append("6. **特殊要求**\n");
            sb.append("   ").append(request.getSpecialRequirements()).append("\n\n");
        }

        sb.append("请确保分析结果：\n");
        sb.append("- 基于真实可靠的信息和数据\n");
        sb.append("- 风险评估准确且有充分依据\n");
        sb.append("- 修改建议具体可操作\n");
        sb.append("- 严格按照以下JSON格式返回结果\n\n");

        return sb.toString();
    }

    /**
     * 构建JSON输出模板
     */
    private String buildJsonTemplate() {
        return """
        【输出格式要求】
        请严格按照以下JSON格式返回审查结果，不要添加任何其他文本：

        {
          "overallRiskLevel": "HIGH/MEDIUM/LOW",
          "riskScore": 85,
          "riskSummary": "整体风险评估总结，简明扼要地描述主要风险和建议",
          "riskPoints": [
            {
              "category": "法律合规/商业风险/财务风险/操作风险/声誉风险",
              "description": "具体风险描述，详细说明风险内容和影响",
              "severity": "HIGH/MEDIUM/LOW",
              "affectedClauses": ["涉及的具体条款内容"],
              "legalBasis": "法律依据或内部制度依据",
              "suggestions": ["具体的修改建议和改进措施"],
              "riskScore": 90,
              "isCritical": true
            }
          ],
          "partyAnalysis": [
            {
              "partyName": "关联方名称",
              "partyType": "COMPANY/INDIVIDUAL",
              "riskLevel": "HIGH/MEDIUM/LOW",
              "riskFactors": ["具体风险因素列表"],
              "complianceIssues": ["合规问题列表"],
              "recommendations": ["针对该关联方的建议措施"],
              "creditRating": "信用等级",
              "businessStatus": "经营状态"
            }
          ],
          "clauseIssues": [
            {
              "clauseText": "有问题的条款具体内容",
              "clauseNumber": "条款编号或位置",
              "issueType": "问题类型分类",
              "description": "问题的详细描述",
              "severity": "HIGH/MEDIUM/LOW",
              "legalRisk": "法律风险说明",
              "suggestions": ["具体修改建议"],
              "referenceLaws": ["相关法律法规"]
            }
          ],
          "complianceCheck": {
            "violatedRegulations": ["可能违反的法律法规"],
            "internalPolicyViolations": ["违反的企业内部制度"],
            "recommendations": ["合规建议"],
            "requiredActions": ["必须采取的行动"],
            "overallCompliance": true,
            "complianceScore": "85/100"
          },
          "recommendations": ["总体建议措施"],
          "nextActions": ["后续行动建议"]
        }
        """;
    }
}
