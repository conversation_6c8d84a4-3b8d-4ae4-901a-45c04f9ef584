package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.service.AiResponseCacheService;
import com.whiskerguard.ai.service.compliance.CompanyInfoDTO;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * RAG (Retrieval-Augmented Generation) 辅助类 - 增强版
 * <p>
 * 该类负责与检索服务交互，获取相关上下文，并将其格式化为LLM可用的提示词。
 * 用于增强LLM的回答质量，减少幻觉。
 *
 * 增强功能：
 * 1. 智能缓存 - 缓存RAG检索结果，避免重复调用
 * 2. 快速失败 - 优化超时处理
 * 3. 性能监控 - 详细的性能指标记录
 */
@Component
public class RagHelper {

    // 日志记录器
    private final Logger log = LoggerFactory.getLogger(RagHelper.class);
    // 检索服务客户端
    private final RetrievalServiceClient retrievalServiceClient;
    // 缓存服务
    private final AiResponseCacheService cacheService;

    // RAG功能是否启用，默认为true
    @Value("${whiskerguard.ai.rag.enabled:true}")
    private boolean ragEnabled;

    // 默认租户ID，当请求中没有提供租户ID时使用
    @Value("${whiskerguard.ai.rag.default-tenant-id:1}")
    private String defaultTenantId;

    // 默认检索结果数量，返回相似度最高的前K个结果
    @Value("${whiskerguard.ai.rag.top-k:3}")
    private Integer defaultTopK;

    // 默认距离计算方式，用于计算向量相似度，默认使用余弦相似度
    @Value("${whiskerguard.ai.rag.distance-metric:cosine}")
    private String defaultDistanceMetric;

    // 最小相似度阈值，低于该阈值的检索结果将被过滤掉
    @Value("${whiskerguard.ai.rag.min-score:0.6}")
    private Float minScore;

    // 快速失败模式，当启用时会更快地跳过不可用的 RAG 服务
    @Value("${whiskerguard.ai.rag.fast-fail:true}")
    private boolean fastFail;

    // RAG 调用超时时间（毫秒）
    @Value("${whiskerguard.ai.rag.timeout:3000}")
    private long ragTimeout;

    /**
     * 构造函数
     *
     * @param retrievalServiceClient 检索服务客户端
     * @param cacheService           缓存服务
     */
    public RagHelper(RetrievalServiceClient retrievalServiceClient, AiResponseCacheService cacheService) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.cacheService = cacheService;
    }

    /**
     * 使用RAG增强提示词
     *
     * @param dto 原始调用请求
     * @return 增强后的提示词
     */
    @CircuitBreaker(name = "rag-service", fallbackMethod = "enhancePromptWithRagFallback")
    @Retry(name = "rag-service")
    public String enhancePromptWithRag(AiInvocationRequestDTO dto) {
        if (!ragEnabled) {
            log.debug("RAG功能已禁用，使用原始提示词");
            return dto.getPrompt();
        }

        try {
            // 获取租户ID，如果请求中没有提供，则使用默认值
            String tenantId = dto.getTenantId() != null ? dto.getTenantId().toString() : defaultTenantId;

            log.debug(
                "开始调用 RAG 检索服务，TenantId: {}, Query: {}",
                tenantId,
                dto.getPrompt().length() > 100 ? dto.getPrompt().substring(0, 100) + "..." : dto.getPrompt()
            );

            // 检查缓存中是否已有结果
            String cachedResponse = cacheService.getCachedRagResult(dto.getPrompt());

            if (cachedResponse != null) {
                log.info("命中缓存，直接返回增强提示词");
                return cachedResponse;
            }

            // 创建检索请求
            RetrieveRequestDTO retrieveRequest = createRetrieveRequest(dto.getPrompt());

            // 调用检索服务
            RetrieveResponseDTO response = retrievalServiceClient.retrieve(tenantId, retrieveRequest);

            log.debug("RAG 检索服务调用成功，返回结果数量: {}", response.getResults() != null ? response.getResults().size() : 0);

            // 如果没有检索到结果，返回原始提示词
            if (response.getResults() == null || response.getResults().isEmpty()) {
                log.debug("未检索到相关上下文，使用原始提示词");
                return dto.getPrompt();
            }

            // 过滤低相似度的结果
            List<RetrieveResponseDTO.Result> filteredResults = response
                .getResults()
                .stream()
                .filter(result -> result.getScore() >= minScore)
                .collect(Collectors.toList());

            if (filteredResults.isEmpty()) {
                log.debug("检索结果相似度低于阈值 {}，使用原始提示词", minScore);
                return dto.getPrompt();
            }

            log.info("RAG 增强成功，使用 {} 个检索结果增强提示词", filteredResults.size());
            // 构建增强提示词
            String enhancedPrompt = buildEnhancedPrompt(dto.getPrompt(), filteredResults);

            // 将结果存入缓存
            cacheService.cacheRagResult(dto.getPrompt(), enhancedPrompt);

            return enhancedPrompt;
        } catch (feign.FeignException.Unauthorized e) {
            log.warn("RAG 服务认证失败 (401)，可能是认证配置问题，使用原始提示词继续处理: {}", e.getMessage());
            return dto.getPrompt();
        } catch (feign.FeignException.NotFound e) {
            log.warn("RAG 服务接口未找到 (404)，可能是服务版本不匹配，使用原始提示词继续处理: {}", e.getMessage());
            return dto.getPrompt();
        } catch (feign.FeignException.ServiceUnavailable e) {
            log.warn("RAG 服务不可用 (503)，快速跳过以避免用户等待，使用原始提示词继续处理");
            return dto.getPrompt();
        } catch (feign.FeignException.InternalServerError e) {
            log.warn("RAG 服务内部错误 (500)，快速跳过以避免用户等待，使用原始提示词继续处理");
            return dto.getPrompt();
        } catch (feign.FeignException e) {
            log.warn("RAG 服务调用失败 (HTTP {})，快速跳过以避免用户等待，使用原始提示词继续处理: {}", e.status(), e.getMessage());
            return dto.getPrompt();
        } catch (Exception e) {
            // 检查是否是连接相关的异常
            Throwable cause = e.getCause();
            if (cause instanceof java.net.ConnectException) {
                log.warn("RAG 服务连接失败，服务可能不可用，快速跳过以避免用户等待: {}", cause.getMessage());
                return dto.getPrompt();
            } else if (cause instanceof java.net.SocketTimeoutException) {
                log.warn("RAG 服务响应超时，快速跳过以避免用户等待: {}", cause.getMessage());
                return dto.getPrompt();
            } else if (e instanceof java.net.ConnectException) {
                log.warn("RAG 服务连接失败，服务可能不可用，快速跳过以避免用户等待: {}", e.getMessage());
                return dto.getPrompt();
            } else if (e instanceof java.net.SocketTimeoutException) {
                log.warn("RAG 服务响应超时，快速跳过以避免用户等待: {}", e.getMessage());
                return dto.getPrompt();
            } else {
                log.error("RAG 增强提示词过程中发生未知错误，快速跳过以避免用户等待，使用原始提示词继续处理", e);
            }
            // 出错时返回原始提示词
            return dto.getPrompt();
        }
    }

    /**
     * RAG 增强提示词的回退方法
     * <p>
     * 当 RAG 服务不可用或熔断器打开时，直接返回原始提示词
     *
     * @param dto 原始调用请求
     * @param ex 异常信息（可选）
     * @return 原始提示词
     */
    public String enhancePromptWithRagFallback(AiInvocationRequestDTO dto, Exception ex) {
        log.warn("RAG 服务熔断器激活或服务不可用，使用原始提示词继续处理: {}", ex != null ? ex.getMessage() : "熔断器打开");
        return dto.getPrompt();
    }

    /**
     * RAG 增强提示词的回退方法（无异常参数版本）
     * <p>
     * 当 RAG 服务不可用或熔断器打开时，直接返回原始提示词
     *
     * @param dto 原始调用请求
     * @return 原始提示词
     */
    public String enhancePromptWithRagFallback(AiInvocationRequestDTO dto) {
        log.warn("RAG 服务熔断器激活，使用原始提示词继续处理");
        return dto.getPrompt();
    }

    /**
     * 为法规到制度转换增强提示词
     *
     * @param legalText 法规文本
     * @param companyInfo 企业信息
     * @param tenantId 租户ID
     * @return 增强后的提示词
     */
    @CircuitBreaker(name = "rag-service", fallbackMethod = "enhancePromptForRegulatoryPolicyFallback")
    @Retry(name = "rag-service")
    public String enhancePromptForRegulatoryPolicy(String legalText, CompanyInfoDTO companyInfo, String tenantId) {
        if (!ragEnabled) {
            log.debug("RAG功能已禁用，使用基础提示词模板");
            return buildBasicRegulatoryPolicyPrompt(legalText, companyInfo);
        }

        try {
            log.debug("开始为法规制度转换创建增强提示词");

            // 创建专门的检索请求
            RetrieveRequestDTO retrieveRequest = createSpecializedRetrieveRequest(legalText, companyInfo.getIndustry());

            // 调用检索服务获取相关法规解释和案例
            RetrieveResponseDTO response = retrievalServiceClient.retrieve(tenantId != null ? tenantId : defaultTenantId, retrieveRequest);

            // 构建增强提示词
            return buildEnhancedRegulatoryPrompt(legalText, companyInfo, response.getResults());
        } catch (Exception e) {
            log.error("法规制度转换RAG增强提示词失败", e);
            // 出错时返回基础提示词模板
            return buildBasicRegulatoryPolicyPrompt(legalText, companyInfo);
        }
    }

    /**
     * 法规制度转换增强提示词的回退方法
     * <p>
     * 当 RAG 服务不可用或熔断器打开时，使用基础提示词模板
     *
     * @param legalText 法规文本
     * @param companyInfo 企业信息
     * @param tenantId 租户ID
     * @param ex 异常信息（可选）
     * @return 基础提示词模板
     */
    public String enhancePromptForRegulatoryPolicyFallback(String legalText, CompanyInfoDTO companyInfo, String tenantId, Exception ex) {
        log.warn("RAG 服务熔断器激活或服务不可用，使用基础提示词模板继续处理: {}", ex != null ? ex.getMessage() : "熔断器打开");
        return buildBasicRegulatoryPolicyPrompt(legalText, companyInfo);
    }

    /**
     * 法规制度转换增强提示词的回退方法（无异常参数版本）
     * <p>
     * 当 RAG 服务不可用或熔断器打开时，使用基础提示词模板
     *
     * @param legalText 法规文本
     * @param companyInfo 企业信息
     * @param tenantId 租户ID
     * @return 基础提示词模板
     */
    public String enhancePromptForRegulatoryPolicyFallback(String legalText, CompanyInfoDTO companyInfo, String tenantId) {
        log.warn("RAG 服务熔断器激活，使用基础提示词模板继续处理");
        return buildBasicRegulatoryPolicyPrompt(legalText, companyInfo);
    }

    /**
     * 创建检索请求对象
     *
     * @param query 查询文本
     * @return 检索请求DTO对象
     */
    private RetrieveRequestDTO createRetrieveRequest(String query) {
        return new RetrieveRequestDTO(
            query, // 查询文本
            defaultTopK, // 返回结果数量
            defaultDistanceMetric, // 距离计算方式
            null, // maxContextLength - 最大上下文长度
            "RAW", // contextFormat - 上下文格式，原始格式
            true, // mergeAdjacentChunks - 合并相邻的文本块
            true // includeMetadata - 包含元数据
        );
    }

    /**
     * 创建专门的法规文本检索请求
     *
     * @param legalText 法规文本
     * @param industry 行业
     * @return 检索请求DTO
     */
    private RetrieveRequestDTO createSpecializedRetrieveRequest(String legalText, String industry) {
        // 提取法规文本的关键部分作为检索查询
        String query = extractRelevantQueryText(legalText);

        // 如果行业信息可用，将其添加到查询中以获取更相关的结果
        if (industry != null && !industry.trim().isEmpty()) {
            query += " " + industry + " 行业";
        }

        return new RetrieveRequestDTO(
            query, // 查询文本
            5, // 返回前5个结果
            defaultDistanceMetric, // 距离计算方式
            8192, // 最大上下文长度（根据需要调整）
            "RAW", // 上下文格式，原始格式
            true, // 合并相邻的文本块
            true // 包含元数据
        );
    }

    /**
     * 从法规文本中提取关键部分作为检索查询
     *
     * @param legalText 法规文本
     * @return 提取的查询文本
     */
    private String extractRelevantQueryText(String legalText) {
        // 简单实现：如果文本太长，取前500个字符
        // 实际项目中，应该实现更复杂的关键信息提取算法
        if (legalText.length() > 500) {
            return legalText.substring(0, 500);
        }
        return legalText;
    }

    /**
     * 构建基础法规制度转换提示词模板（无检索结果时使用）
     *
     * @param legalText 法规文本
     * @param companyInfo 企业信息
     * @return 基础提示词
     */
    private String buildBasicRegulatoryPolicyPrompt(String legalText, CompanyInfoDTO companyInfo) {
        StringBuilder promptTemplate = new StringBuilder();

        // 添加系统指令
        promptTemplate.append("你是一位精通法律法规和企业合规的专家，熟悉中国的法律框架以及企业内部制度制定的最佳实践。");
        promptTemplate.append("你的任务是将提供的法律法规内容转换为适合特定企业的内部管理制度，确保内容专业、严谨、合规且可执行。\n\n");

        // 添加企业背景信息
        promptTemplate.append("### 企业背景信息：\n");
        promptTemplate.append("企业名称：").append(companyInfo.getName()).append("\n");
        if (companyInfo.getIndustry() != null) {
            promptTemplate.append("所属行业：").append(companyInfo.getIndustry()).append("\n");
        }
        if (companyInfo.getType() != null) {
            promptTemplate.append("企业类型：").append(companyInfo.getType()).append("\n");
        }
        if (companyInfo.getScale() != null) {
            promptTemplate.append("企业规模：").append(companyInfo.getScale()).append("\n");
        }
        if (companyInfo.getEmployeeCount() != null) {
            promptTemplate.append("员工数量：").append(companyInfo.getEmployeeCount()).append("\n");
        }
        if (companyInfo.getRegLocation() != null) {
            promptTemplate.append("注册地：").append(companyInfo.getRegLocation()).append("\n");
        }
        if (companyInfo.getBusinessScope() != null) {
            promptTemplate.append("经营范围：").append(companyInfo.getBusinessScope()).append("\n");
        }
        if (companyInfo.getOrganizationalStructure() != null) {
            promptTemplate.append("组织架构：").append(companyInfo.getOrganizationalStructure()).append("\n");
        }
        if (companyInfo.getExistingPolicies() != null) {
            promptTemplate.append("现有制度：").append(companyInfo.getExistingPolicies()).append("\n");
        }
        promptTemplate.append("\n");

        // 添加法规文本
        promptTemplate.append("### 法律法规原文：\n").append(legalText).append("\n\n");

        // 添加详细指导
        promptTemplate.append("### 制度制定指南：\n");
        promptTemplate.append("1. 制度的内容必须基于提供的法律法规，确保合规性\n");
        promptTemplate.append("2. 制度应当贴合企业的实际情况和特点\n");
        promptTemplate.append("3. 使用清晰、准确的语言，避免歧义\n");
        promptTemplate.append("4. 制度结构应包括：总则、正文（各章节）、附则\n");
        promptTemplate.append("5. 明确规定各项制度的责任主体、流程要求和违规后果\n");
        promptTemplate.append("6. 引用法律法规时，应当准确标注出处\n");
        promptTemplate.append("7. 制度应当可操作、可执行，避免过于抽象\n\n");

        // 添加输出要求
        promptTemplate.append("### 请根据上述信息，生成一份企业内部管理制度：\n");
        promptTemplate.append("制度应包含以下部分：\n");
        promptTemplate.append("- 制度名称\n");
        promptTemplate.append("- 制度目的和依据\n");
        promptTemplate.append("- 适用范围\n");
        promptTemplate.append("- 详细规定（各章节）\n");
        promptTemplate.append("- 责任与义务\n");
        promptTemplate.append("- 实施与监督\n");
        promptTemplate.append("- 附则\n\n");

        return promptTemplate.toString();
    }

    /**
     * 构建增强的法规制度转换提示词
     *
     * @param legalText 法规文本
     * @param companyInfo 企业信息
     * @param results 检索结果
     * @return 增强后的提示词
     */
    private String buildEnhancedRegulatoryPrompt(String legalText, CompanyInfoDTO companyInfo, List<RetrieveResponseDTO.Result> results) {
        // 首先构建基础提示词
        StringBuilder promptTemplate = new StringBuilder(buildBasicRegulatoryPolicyPrompt(legalText, companyInfo));

        // 如果检索结果不为空，添加相关法规解释和案例
        if (results != null && !results.isEmpty()) {
            List<RetrieveResponseDTO.Result> filteredResults = results
                .stream()
                .filter(result -> result.getScore() >= minScore)
                .collect(Collectors.toList());

            if (!filteredResults.isEmpty()) {
                promptTemplate.append("### 相关法规解释和案例：\n");
                for (int i = 0; i < filteredResults.size(); i++) {
                    RetrieveResponseDTO.Result result = filteredResults.get(i);
                    promptTemplate.append("参考资料 ").append(i + 1).append("：\n");
                    promptTemplate.append(result.getText()).append("\n");

                    // 添加来源信息（如果有）
                    if (result.getMetadata() != null && !result.getMetadata().isEmpty()) {
                        String source = extractSourceFromMetadata(result.getMetadata());
                        if (source != null) {
                            promptTemplate.append("来源：").append(source).append("\n");
                        }
                    }
                    promptTemplate.append("\n");
                }

                promptTemplate.append("请参考上述相关法规解释和案例，确保制定的制度既符合法规要求，又具有实践指导意义。\n\n");
            }
        }

        return promptTemplate.toString();
    }

    /**
     * 构建增强提示词
     *
     * @param originalPrompt 原始提示词
     * @param results 检索结果
     * @return 增强后的提示词
     */
    private String buildEnhancedPrompt(String originalPrompt, List<RetrieveResponseDTO.Result> results) {
        StringBuilder enhancedPrompt = new StringBuilder();

        // 添加系统指令
        enhancedPrompt.append(
            "我将为你提供一些相关的上下文信息，请基于这些信息回答我的问题。如果上下文中没有足够的信息，请明确指出并尽可能给出合理的回答。\n\n"
        );

        // 添加上下文信息
        enhancedPrompt.append("### 相关上下文：\n");
        for (int i = 0; i < results.size(); i++) {
            RetrieveResponseDTO.Result result = results.get(i);
            enhancedPrompt.append("文档 ").append(i + 1).append("：\n");
            enhancedPrompt.append(result.getText()).append("\n");

            // 添加文档来源信息（如果有）
            if (result.getMetadata() != null && !result.getMetadata().isEmpty()) {
                String source = extractSourceFromMetadata(result.getMetadata());
                if (source != null) {
                    enhancedPrompt.append("来源：").append(source).append("\n");
                }
            }
            enhancedPrompt.append("\n");
        }

        // 添加原始问题
        enhancedPrompt.append("### 问题：\n");
        enhancedPrompt.append(originalPrompt).append("\n\n");

        // 添加回答指示
        enhancedPrompt.append("### 请基于上述上下文回答问题：\n");

        return enhancedPrompt.toString();
    }

    /**
     * 从元数据中提取来源信息
     *
     * @param metadata 元数据映射
     * @return 文档来源信息，如果没有则返回null
     */
    private String extractSourceFromMetadata(Map<String, Object> metadata) {
        if (metadata.containsKey("source")) {
            return metadata.get("source").toString();
        }
        return null;
    }
}
