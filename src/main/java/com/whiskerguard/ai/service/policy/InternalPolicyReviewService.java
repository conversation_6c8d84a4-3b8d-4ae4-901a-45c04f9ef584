/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyReviewService.java
 * 包    名：com.whiskerguard.ai.service.policy
 * 描    述：内部制度智能审查服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.policy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.CompanyInfoDTO;
import com.whiskerguard.ai.client.dto.InternalPolicyDTO;
import com.whiskerguard.ai.client.dto.LegalRegulationDTO;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.service.dto.policy.PolicyClauseIssueDTO;
import com.whiskerguard.ai.service.dto.policy.PolicyComplianceAssessmentDTO;
import com.whiskerguard.ai.service.dto.policy.PolicyRelatedPartyRiskDTO;
import com.whiskerguard.ai.service.dto.policy.PolicyRiskPointDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 内部制度智能审查服务
 * <p>
 * 提供内部制度的智能审查功能，包括：
 * 1. 制度内容分析和结构化处理
 * 2. 法律法规合规性检查
 * 3. 关联方风险评估
 * 4. 条款问题识别和改进建议
 *
 * 该服务整合了多个数据源：
 * - 法规微服务：获取相关法律法规
 * - 通用微服务：获取企业信息
 * - RAG检索服务：获取历史案例和最佳实践
 * - AI模型：进行智能分析和建议生成
 */
@Service
@Transactional
public class InternalPolicyReviewService {

    private static final Logger log = LoggerFactory.getLogger(InternalPolicyReviewService.class);

    private final AiInvocationService aiInvocationService;
    private final RegulatoryServiceClient regulatoryServiceClient;
    private final GeneralServiceClient generalServiceClient;
    private final RetrievalServiceClient retrievalServiceClient;
    private final PolicyAnalysisService policyAnalysisService;
    private final PolicyRiskAssessmentService policyRiskAssessmentService;
    private final RelatedPartyService relatedPartyService;
    private final AiToolMetricsService metricsService;
    private final ObjectMapper objectMapper;
    private final PromptBuilderService promptBuilderService;

    public InternalPolicyReviewService(
        AiInvocationService aiInvocationService,
        RegulatoryServiceClient regulatoryServiceClient,
        GeneralServiceClient generalServiceClient,
        RetrievalServiceClient retrievalServiceClient,
        PolicyAnalysisService policyAnalysisService,
        PolicyRiskAssessmentService policyRiskAssessmentService,
        RelatedPartyService relatedPartyService,
        AiToolMetricsService metricsService,
        ObjectMapper objectMapper,
        PromptBuilderService promptBuilderService
    ) {
        this.aiInvocationService = aiInvocationService;
        this.regulatoryServiceClient = regulatoryServiceClient;
        this.generalServiceClient = generalServiceClient;
        this.retrievalServiceClient = retrievalServiceClient;
        this.policyAnalysisService = policyAnalysisService;
        this.policyRiskAssessmentService = policyRiskAssessmentService;
        this.relatedPartyService = relatedPartyService;
        this.metricsService = metricsService;
        this.objectMapper = objectMapper;
        this.promptBuilderService = promptBuilderService;
    }

    /**
     * 执行内部制度智能审查
     *
     * @param request 审查请求
     * @return 审查结果
     */
    public InternalPolicyReviewResponseDTO reviewInternalPolicy(InternalPolicyReviewRequestDTO request) {
        long startTime = System.currentTimeMillis();

        // 1. 参数验证 - 在日志之前进行，避免空指针异常
        validateRequest(request);

        log.info("开始内部制度智能审查，租户ID: {}, 制度类型: {}", request.getTenantId(), request.getPolicyType());

        try {
            // 2. 制度内容分析
            PolicyAnalysisContext analysisContext = policyAnalysisService.analyzePolicyContent(request);
            log.debug("制度内容分析完成，识别到 {} 个关键条款", analysisContext.getKeyClauses().size());

            // 3. 获取法律法规上下文
            LegalRegulatoryContext legalContext = getLegalRegulatoryContext(request);
            log.debug("法律法规上下文获取完成，相关法规数量: {}", legalContext.getApplicableLaws().size());

            // 4. 获取关联方信息
            RelatedPartyContext relatedPartyContext = relatedPartyService.getRelatedPartyContext(request);
            log.debug("关联方信息获取完成，关联方数量: {}", relatedPartyContext.getRelatedParties().size());

            // 5. RAG增强信息检索
            RagEnhancedContext ragContext = getRagEnhancedContext(request, analysisContext);
            log.debug("RAG增强信息检索完成，检索结果数量: {}", ragContext.getRetrievalResults().size());

            // 6. 构建AI分析提示词
            String enhancedPrompt = buildEnhancedPrompt(request, analysisContext, legalContext, relatedPartyContext, ragContext);

            // 7. 调用AI进行智能分析
            AiRequestDTO aiResult = invokeAiAnalysis(request, enhancedPrompt);
            log.debug("AI分析完成，响应长度: {}", aiResult.getResponse() != null ? aiResult.getResponse().length() : 0);

            // 8. 解析AI响应并构建结果
            InternalPolicyReviewResponseDTO response = parseAiResponseAndBuildResult(
                aiResult,
                analysisContext,
                legalContext,
                relatedPartyContext
            );

            // 9. 设置审查元数据
            long duration = System.currentTimeMillis() - startTime;
            response.setReviewTime(Instant.now());
            response.setReviewDuration(duration);
            response.setReviewStatus("COMPLETED");
            response.setAiModelInfo(aiResult.getToolType());

            log.info(
                "内部制度智能审查完成，租户ID: {}, 耗时: {}ms, 风险分数: {}",
                request.getTenantId(),
                duration,
                response.getRiskScore()
            );

            return response;
        } catch (Exception e) {
            log.error("内部制度智能审查失败，租户ID: {}, 错误: {}", request.getTenantId(), e.getMessage(), e);
            throw new PolicyReviewException("内部制度智能审查失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(InternalPolicyReviewRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("审查请求不能为空");
        }
        if (request.getPolicyContent() == null || request.getPolicyContent().trim().isEmpty()) {
            throw new IllegalArgumentException("制度内容不能为空");
        }
        if (request.getTenantId() == null) {
            throw new IllegalArgumentException("租户ID不能为空");
        }
        if (request.getEmployeeId() == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }
    }

    /**
     * 获取法律法规上下文
     */
    private LegalRegulatoryContext getLegalRegulatoryContext(InternalPolicyReviewRequestDTO request) {
        try {
            LegalRegulatoryContext context = new LegalRegulatoryContext();

            // 根据制度类型获取相关法律法规（使用现有的合同类型方法作为替代）
            List<LegalRegulationDTO> applicableLaws = new ArrayList<>();
            try {
                // 尝试通过合同类型获取相关法规（作为制度类型的替代）
                applicableLaws = regulatoryServiceClient.getRegulationsByContractType(request.getPolicyType(), request.getTenantId());
            } catch (Exception e) {
                log.debug("通过制度类型获取法规失败，尝试其他方式: {}", e.getMessage());
            }
            context.setApplicableLaws(applicableLaws);

            // 根据行业获取相关法规
            if (request.getIndustry() != null) {
                List<LegalRegulationDTO> industryRegulations = regulatoryServiceClient.getRegulationsByIndustry(
                    request.getIndustry(),
                    request.getTenantId()
                );
                context.setIndustryRegulations(industryRegulations);
            }

            // 获取内部制度
            List<InternalPolicyDTO> internalPolicies = regulatoryServiceClient.getInternalPolicies(
                request.getTenantId(),
                request.getPolicyType()
            );
            context.setInternalPolicies(internalPolicies);

            return context;
        } catch (Exception e) {
            log.warn("获取法律法规上下文失败: {}", e.getMessage());
            return new LegalRegulatoryContext(); // 返回空上下文，不影响主流程
        }
    }

    /**
     * 获取RAG增强上下文
     */
    private RagEnhancedContext getRagEnhancedContext(InternalPolicyReviewRequestDTO request, PolicyAnalysisContext analysisContext) {
        try {
            RagEnhancedContext context = new RagEnhancedContext();

            // 构建检索查询
            String ragQuery = String.format("制度类型：%s 制度审查 风险识别 合规检查 最佳实践 案例分析", request.getPolicyType());

            // 调用RAG检索服务
            RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 5, "cosine");
            RetrieveResponseDTO ragResponse = retrievalServiceClient.retrieve(request.getTenantId().toString(), ragRequest);

            context.setRetrievalResults(ragResponse.getResults());
            context.setMergedContext(ragResponse.getMergedContext());

            return context;
        } catch (Exception e) {
            log.warn("RAG增强信息检索失败: {}", e.getMessage());
            return new RagEnhancedContext(); // 返回空上下文，不影响主流程
        }
    }

    /**
     * 构建增强的AI分析提示词
     * 现在使用模板化方式，向后兼容硬编码方式
     */
    private String buildEnhancedPrompt(
        InternalPolicyReviewRequestDTO request,
        PolicyAnalysisContext analysisContext,
        LegalRegulatoryContext legalContext,
        RelatedPartyContext relatedPartyContext,
        RagEnhancedContext ragContext
    ) {
        try {
            // 尝试使用新的模板化方式
            return buildTemplatedPrompt(request, analysisContext, legalContext, relatedPartyContext, ragContext);
        } catch (Exception e) {
            log.warn("模板化提示词构建失败，回退到硬编码方式: {}", e.getMessage());
            // 回退到原有的硬编码方式
            return buildLegacyPrompt(request, analysisContext, legalContext, relatedPartyContext, ragContext);
        }
    }

    /**
     * 使用模板化方式构建提示词
     */
    private String buildTemplatedPrompt(
        InternalPolicyReviewRequestDTO request,
        PolicyAnalysisContext analysisContext,
        LegalRegulatoryContext legalContext,
        RelatedPartyContext relatedPartyContext,
        RagEnhancedContext ragContext
    ) {
        // 构建变量映射
        Map<String, Object> variables = new HashMap<>();

        // 基础制度信息
        variables.put("POLICY_CONTENT", request.getPolicyContent());
        variables.put("POLICY_TYPE", request.getPolicyType());
        variables.put("DEPARTMENT", request.getDepartment());

        // 企业信息
        if (request.getCompanyName() != null) {
            variables.put("COMPANY_NAME", request.getCompanyName());
        }
        if (request.getIndustry() != null) {
            variables.put("INDUSTRY", request.getIndustry());
        }

        // 制度标题
        if (request.getPolicyTitle() != null) {
            variables.put("POLICY_TITLE", request.getPolicyTitle());
        }

        // 法律法规信息
        if (legalContext.getApplicableLaws() != null && !legalContext.getApplicableLaws().isEmpty()) {
            StringBuilder legalInfo = new StringBuilder();
            for (LegalRegulationDTO law : legalContext.getApplicableLaws()) {
                legalInfo.append("- ").append(law.getName()).append("：").append(law.getFullText()).append("\n");
            }
            variables.put("LEGAL_REGULATIONS", legalInfo.toString());
            variables.put("HAS_LEGAL_CONTEXT", true);
        } else {
            variables.put("HAS_LEGAL_CONTEXT", false);
        }

        // 关联方信息
        if (relatedPartyContext.getRelatedParties() != null && !relatedPartyContext.getRelatedParties().isEmpty()) {
            StringBuilder partyInfo = new StringBuilder();
            relatedPartyContext.getRelatedParties().forEach(party -> {
                partyInfo.append("- ").append(party.getName()).append("（").append(party.getType()).append("）\n");
            });
            variables.put("RELATED_PARTIES", partyInfo.toString());
            variables.put("HAS_RELATED_PARTIES", true);
        } else {
            variables.put("HAS_RELATED_PARTIES", false);
        }

        // RAG增强信息
        if (ragContext.getMergedContext() != null && !ragContext.getMergedContext().trim().isEmpty()) {
            variables.put("RAG_CONTEXT", ragContext.getMergedContext());
            variables.put("HAS_RAG_CONTEXT", true);
        } else {
            variables.put("HAS_RAG_CONTEXT", false);
        }

        // 使用模板构建服务
        PromptBuildRequest buildRequest = PromptBuildRequest.builder()
            .templateType(PromptTemplateType.POLICY_INTERNAL_REVIEW)
            .tenantId(request.getTenantId())
            .variables(variables)
            .enableRagEnhancement(false) // 这里不启用RAG，因为已经在上层处理
            .buildContext("内部制度审查")
            .build();

        return promptBuilderService.buildPrompt(buildRequest);
    }

    /**
     * 使用硬编码方式构建提示词（回退方案）
     */
    private String buildLegacyPrompt(
        InternalPolicyReviewRequestDTO request,
        PolicyAnalysisContext analysisContext,
        LegalRegulatoryContext legalContext,
        RelatedPartyContext relatedPartyContext,
        RagEnhancedContext ragContext
    ) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("# 内部制度智能审查任务\n\n");
        prompt.append("请对以下内部制度进行全面的智能审查分析：\n\n");

        // 制度基本信息
        prompt.append("## 制度基本信息\n");
        prompt.append("- 制度类型：").append(request.getPolicyType()).append("\n");
        if (request.getPolicyTitle() != null) {
            prompt.append("- 制度标题：").append(request.getPolicyTitle()).append("\n");
        }
        if (request.getDepartment() != null) {
            prompt.append("- 制定部门：").append(request.getDepartment()).append("\n");
        }
        if (request.getCompanyName() != null) {
            prompt.append("- 公司名称：").append(request.getCompanyName()).append("\n");
        }
        if (request.getIndustry() != null) {
            prompt.append("- 行业类型：").append(request.getIndustry()).append("\n");
        }
        prompt.append("\n");

        // 制度内容
        prompt.append("## 制度内容\n");
        prompt.append("```\n").append(request.getPolicyContent()).append("\n```\n\n");

        // 相关法律法规
        if (legalContext.getApplicableLaws() != null && !legalContext.getApplicableLaws().isEmpty()) {
            prompt.append("## 相关法律法规\n");
            for (LegalRegulationDTO law : legalContext.getApplicableLaws()) {
                prompt.append("- ").append(law.getName()).append("：").append(law.getFullText()).append("\n");
            }
            prompt.append("\n");
        }

        // 关联方信息
        if (relatedPartyContext.getRelatedParties() != null && !relatedPartyContext.getRelatedParties().isEmpty()) {
            prompt.append("## 关联方信息\n");
            relatedPartyContext
                .getRelatedParties()
                .forEach(party -> {
                    prompt.append("- ").append(party.getName()).append("（").append(party.getType()).append("）\n");
                });
            prompt.append("\n");
        }

        // RAG检索的补充信息
        if (ragContext.getMergedContext() != null && !ragContext.getMergedContext().trim().isEmpty()) {
            prompt.append("## 参考信息和最佳实践\n");
            prompt.append(ragContext.getMergedContext()).append("\n\n");
        }

        // 分析要求
        prompt.append("## 分析要求\n");
        prompt.append("请按照以下结构进行详细分析，并以JSON格式返回结果：\n\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"overallRiskLevel\": \"LOW|MEDIUM|HIGH|CRITICAL\",\n");
        prompt.append("  \"riskScore\": 0-100,\n");
        prompt.append("  \"riskSummary\": \"整体风险总结\",\n");
        prompt.append("  \"riskPoints\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"riskId\": \"风险点ID\",\n");
        prompt.append("      \"riskType\": \"风险类型\",\n");
        prompt.append("      \"riskLevel\": \"风险等级\",\n");
        prompt.append("      \"title\": \"风险标题\",\n");
        prompt.append("      \"description\": \"风险描述\",\n");
        prompt.append("      \"affectedClauses\": [\"相关条款\"],\n");
        prompt.append("      \"recommendations\": [\"改进建议\"]\n");
        prompt.append("    }\n");
        prompt.append("  ],\n");
        prompt.append("  \"clauseIssues\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"issueId\": \"问题ID\",\n");
        prompt.append("      \"clauseNumber\": \"条款编号\",\n");
        prompt.append("      \"issueType\": \"问题类型\",\n");
        prompt.append("      \"severity\": \"严重程度\",\n");
        prompt.append("      \"issueDescription\": \"问题描述\",\n");
        prompt.append("      \"modificationSuggestion\": \"修改建议\"\n");
        prompt.append("    }\n");
        prompt.append("  ],\n");
        prompt.append("  \"overallRecommendations\": [\"总体建议\"],\n");
        prompt.append("  \"priorityActions\": [\"优先处理事项\"],\n");
        prompt.append("  \"confidence\": 0-100\n");
        prompt.append("}\n");
        prompt.append("```\n\n");

        prompt.append("请确保分析结果准确、详细、可操作。");

        return prompt.toString();
    }

    /**
     * 调用AI进行智能分析
     */
    private AiRequestDTO invokeAiAnalysis(InternalPolicyReviewRequestDTO request, String enhancedPrompt) {
        try {
            // 构建AI调用请求
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO();
            aiRequest.setToolKey("deepseek"); // 使用DeepSeek模型进行制度审查
            aiRequest.setPrompt(enhancedPrompt);
            aiRequest.setTenantId(request.getTenantId());
            aiRequest.setEmployeeId(request.getEmployeeId());

            // 设置元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("reviewType", "internal_policy");
            metadata.put("policyType", request.getPolicyType());
            metadata.put("department", request.getDepartment());
            metadata.put("priority", request.getPriority());
            aiRequest.setMetadata(metadata);

            // 调用AI服务
            return aiInvocationService.invoke(aiRequest);
        } catch (Exception e) {
            log.error("AI分析调用失败: {}", e.getMessage(), e);
            throw new PolicyReviewException("AI分析调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析AI响应并构建结果
     */
    private InternalPolicyReviewResponseDTO parseAiResponseAndBuildResult(
        AiRequestDTO aiResult,
        PolicyAnalysisContext analysisContext,
        LegalRegulatoryContext legalContext,
        RelatedPartyContext relatedPartyContext
    ) {
        try {
            // 解析AI响应的JSON结果
            Map<String, Object> aiResponse = parseAiJsonResponse(aiResult.getResponse());

            // 构建响应对象
            InternalPolicyReviewResponseDTO response = new InternalPolicyReviewResponseDTO();
            response.setReviewId(aiResult.getId());

            // 设置整体风险评估
            String riskLevelStr = (String) aiResponse.get("overallRiskLevel");
            if (riskLevelStr != null) {
                response.setOverallRiskLevel(InternalPolicyReviewResponseDTO.OverallRiskLevel.valueOf(riskLevelStr));
            }

            response.setRiskScore((Integer) aiResponse.get("riskScore"));
            response.setRiskSummary((String) aiResponse.get("riskSummary"));
            response.setConfidence((Integer) aiResponse.get("confidence"));

            // 解析风险点
            List<Map<String, Object>> riskPointsData = (List<Map<String, Object>>) aiResponse.get("riskPoints");
            if (riskPointsData != null) {
                List<PolicyRiskPointDTO> riskPoints = parseRiskPoints(riskPointsData);
                response.setRiskPoints(riskPoints);
            }

            // 解析条款问题
            List<Map<String, Object>> clauseIssuesData = (List<Map<String, Object>>) aiResponse.get("clauseIssues");
            if (clauseIssuesData != null) {
                List<PolicyClauseIssueDTO> clauseIssues = parseClauseIssues(clauseIssuesData);
                response.setClauseIssues(clauseIssues);
            }

            // 解析关联方风险
            List<PolicyRelatedPartyRiskDTO> relatedPartyRisks = policyRiskAssessmentService.assessRelatedPartyRisks(relatedPartyContext);
            response.setRelatedPartyRisks(relatedPartyRisks);

            // 构建合规性评估
            PolicyComplianceAssessmentDTO complianceAssessment = buildComplianceAssessment(legalContext, aiResponse);
            response.setComplianceAssessment(complianceAssessment);

            // 设置建议
            response.setOverallRecommendations((List<String>) aiResponse.get("overallRecommendations"));
            response.setPriorityActions((List<String>) aiResponse.get("priorityActions"));

            // 设置其他信息
            response.setExecutiveSummary(buildExecutiveSummary(response));
            response.setDataSources(Arrays.asList("AI分析", "法规数据库", "RAG检索", "企业信息"));

            return response;
        } catch (Exception e) {
            log.error("解析AI响应失败: {}", e.getMessage(), e);
            throw new PolicyReviewException("解析AI响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析AI的JSON响应
     */
    private Map<String, Object> parseAiJsonResponse(String aiResponse) {
        try {
            // 提取JSON部分（去除可能的markdown格式）
            String jsonContent = extractJsonFromResponse(aiResponse);
            return objectMapper.readValue(jsonContent, Map.class);
        } catch (Exception e) {
            log.error("解析AI JSON响应失败: {}", e.getMessage());
            // 返回默认结果
            Map<String, Object> defaultResponse = new HashMap<>();
            defaultResponse.put("overallRiskLevel", "MEDIUM");
            defaultResponse.put("riskScore", 50);
            defaultResponse.put("riskSummary", "AI响应解析失败，请人工审查");
            defaultResponse.put("confidence", 30);
            defaultResponse.put("riskPoints", new ArrayList<>());
            defaultResponse.put("clauseIssues", new ArrayList<>());
            defaultResponse.put("overallRecommendations", Arrays.asList("建议人工重新审查"));
            defaultResponse.put("priorityActions", Arrays.asList("联系技术支持"));
            return defaultResponse;
        }
    }

    /**
     * 从AI响应中提取JSON内容
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return "{}";
        }

        // 查找JSON开始和结束位置
        int jsonStart = response.indexOf("{");
        int jsonEnd = response.lastIndexOf("}");

        if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
            return response.substring(jsonStart, jsonEnd + 1);
        }

        // 如果没有找到完整的JSON，返回空对象
        return "{}";
    }

    /**
     * 解析风险点数据
     */
    private List<PolicyRiskPointDTO> parseRiskPoints(List<Map<String, Object>> riskPointsData) {
        List<PolicyRiskPointDTO> riskPoints = new ArrayList<>();

        for (Map<String, Object> data : riskPointsData) {
            PolicyRiskPointDTO riskPoint = new PolicyRiskPointDTO();
            riskPoint.setRiskId((String) data.get("riskId"));
            riskPoint.setTitle((String) data.get("title"));
            riskPoint.setDescription((String) data.get("description"));

            // 解析枚举类型
            String riskTypeStr = (String) data.get("riskType");
            if (riskTypeStr != null) {
                try {
                    riskPoint.setRiskType(PolicyRiskPointDTO.RiskType.valueOf(riskTypeStr));
                } catch (IllegalArgumentException e) {
                    riskPoint.setRiskType(PolicyRiskPointDTO.RiskType.OTHER);
                }
            }

            String riskLevelStr = (String) data.get("riskLevel");
            if (riskLevelStr != null) {
                try {
                    riskPoint.setRiskLevel(PolicyRiskPointDTO.RiskLevel.valueOf(riskLevelStr));
                } catch (IllegalArgumentException e) {
                    riskPoint.setRiskLevel(PolicyRiskPointDTO.RiskLevel.MEDIUM);
                }
            }

            riskPoint.setAffectedClauses((List<String>) data.get("affectedClauses"));
            riskPoint.setRecommendations((List<String>) data.get("recommendations"));

            riskPoints.add(riskPoint);
        }

        return riskPoints;
    }

    /**
     * 解析条款问题数据
     */
    private List<PolicyClauseIssueDTO> parseClauseIssues(List<Map<String, Object>> clauseIssuesData) {
        List<PolicyClauseIssueDTO> clauseIssues = new ArrayList<>();

        for (Map<String, Object> data : clauseIssuesData) {
            PolicyClauseIssueDTO clauseIssue = new PolicyClauseIssueDTO();
            clauseIssue.setIssueId((String) data.get("issueId"));
            clauseIssue.setClauseNumber((String) data.get("clauseNumber"));
            clauseIssue.setIssueDescription((String) data.get("issueDescription"));
            clauseIssue.setModificationSuggestion((String) data.get("modificationSuggestion"));

            // 解析枚举类型
            String issueTypeStr = (String) data.get("issueType");
            if (issueTypeStr != null) {
                try {
                    clauseIssue.setIssueType(PolicyClauseIssueDTO.IssueType.valueOf(issueTypeStr));
                } catch (IllegalArgumentException e) {
                    clauseIssue.setIssueType(PolicyClauseIssueDTO.IssueType.OTHER);
                }
            }

            String severityStr = (String) data.get("severity");
            if (severityStr != null) {
                try {
                    clauseIssue.setSeverity(PolicyClauseIssueDTO.SeverityLevel.valueOf(severityStr));
                } catch (IllegalArgumentException e) {
                    clauseIssue.setSeverity(PolicyClauseIssueDTO.SeverityLevel.MEDIUM);
                }
            }

            clauseIssues.add(clauseIssue);
        }

        return clauseIssues;
    }

    /**
     * 构建合规性评估
     */
    private PolicyComplianceAssessmentDTO buildComplianceAssessment(LegalRegulatoryContext legalContext, Map<String, Object> aiResponse) {
        PolicyComplianceAssessmentDTO assessment = new PolicyComplianceAssessmentDTO();

        // 基于AI分析结果和法规上下文构建合规性评估
        Integer riskScore = (Integer) aiResponse.get("riskScore");
        if (riskScore != null) {
            // 根据风险分数确定合规等级
            if (riskScore <= 20) {
                assessment.setOverallComplianceLevel(PolicyComplianceAssessmentDTO.ComplianceLevel.FULLY_COMPLIANT);
            } else if (riskScore <= 40) {
                assessment.setOverallComplianceLevel(PolicyComplianceAssessmentDTO.ComplianceLevel.MOSTLY_COMPLIANT);
            } else if (riskScore <= 70) {
                assessment.setOverallComplianceLevel(PolicyComplianceAssessmentDTO.ComplianceLevel.PARTIALLY_COMPLIANT);
            } else {
                assessment.setOverallComplianceLevel(PolicyComplianceAssessmentDTO.ComplianceLevel.NON_COMPLIANT);
            }

            assessment.setComplianceScore(100 - riskScore); // 合规分数 = 100 - 风险分数
        }

        // 构建法律合规性
        PolicyComplianceAssessmentDTO.LegalComplianceDTO legalCompliance = new PolicyComplianceAssessmentDTO.LegalComplianceDTO();
        if (legalContext.getApplicableLaws() != null) {
            List<String> applicableLaws = legalContext.getApplicableLaws().stream().map(LegalRegulationDTO::getName).toList();
            legalCompliance.setApplicableLaws(applicableLaws);
        }
        legalCompliance.setStatus(assessment.getOverallComplianceLevel());
        assessment.setLegalCompliance(legalCompliance);

        return assessment;
    }

    /**
     * 构建执行摘要
     */
    private String buildExecutiveSummary(InternalPolicyReviewResponseDTO response) {
        StringBuilder summary = new StringBuilder();

        summary.append("## 内部制度审查执行摘要\n\n");

        // 整体评估
        summary.append("**整体风险等级：** ").append(response.getOverallRiskLevel()).append("\n");
        summary.append("**风险分数：** ").append(response.getRiskScore()).append("/100\n\n");

        // 主要发现
        summary.append("**主要发现：**\n");
        if (response.getRiskPoints() != null && !response.getRiskPoints().isEmpty()) {
            summary.append("- 识别出 ").append(response.getRiskPoints().size()).append(" 个风险点\n");
        }
        if (response.getClauseIssues() != null && !response.getClauseIssues().isEmpty()) {
            summary.append("- 发现 ").append(response.getClauseIssues().size()).append(" 个条款问题\n");
        }
        if (response.getRelatedPartyRisks() != null && !response.getRelatedPartyRisks().isEmpty()) {
            summary.append("- 识别出 ").append(response.getRelatedPartyRisks().size()).append(" 个关联方风险\n");
        }

        // 建议行动
        summary.append("\n**建议行动：**\n");
        if (response.getPriorityActions() != null) {
            response.getPriorityActions().forEach(action -> summary.append("- ").append(action).append("\n"));
        }

        return summary.toString();
    }

    // 内部上下文类
    public static class PolicyAnalysisContext {

        private List<String> keyClauses = new ArrayList<>();
        private List<String> departments = new ArrayList<>();
        private List<String> processes = new ArrayList<>();
        private Map<String, Object> metadata = new HashMap<>();

        // Getters and Setters
        public List<String> getKeyClauses() {
            return keyClauses;
        }

        public void setKeyClauses(List<String> keyClauses) {
            this.keyClauses = keyClauses;
        }

        public List<String> getDepartments() {
            return departments;
        }

        public void setDepartments(List<String> departments) {
            this.departments = departments;
        }

        public List<String> getProcesses() {
            return processes;
        }

        public void setProcesses(List<String> processes) {
            this.processes = processes;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }

    public static class LegalRegulatoryContext {

        private List<LegalRegulationDTO> applicableLaws = new ArrayList<>();
        private List<LegalRegulationDTO> industryRegulations = new ArrayList<>();
        private List<InternalPolicyDTO> internalPolicies = new ArrayList<>();

        // Getters and Setters
        public List<LegalRegulationDTO> getApplicableLaws() {
            return applicableLaws;
        }

        public void setApplicableLaws(List<LegalRegulationDTO> applicableLaws) {
            this.applicableLaws = applicableLaws;
        }

        public List<LegalRegulationDTO> getIndustryRegulations() {
            return industryRegulations;
        }

        public void setIndustryRegulations(List<LegalRegulationDTO> industryRegulations) {
            this.industryRegulations = industryRegulations;
        }

        public List<InternalPolicyDTO> getInternalPolicies() {
            return internalPolicies;
        }

        public void setInternalPolicies(List<InternalPolicyDTO> internalPolicies) {
            this.internalPolicies = internalPolicies;
        }
    }

    public static class RelatedPartyContext {

        private List<RelatedPartyInfo> relatedParties = new ArrayList<>();

        // Getters and Setters
        public List<RelatedPartyInfo> getRelatedParties() {
            return relatedParties;
        }

        public void setRelatedParties(List<RelatedPartyInfo> relatedParties) {
            this.relatedParties = relatedParties;
        }

        public static class RelatedPartyInfo {

            private String name;
            private String type;
            private Map<String, Object> details = new HashMap<>();

            public RelatedPartyInfo(String name, String type) {
                this.name = name;
                this.type = type;
            }

            // Getters and Setters
            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public Map<String, Object> getDetails() {
                return details;
            }

            public void setDetails(Map<String, Object> details) {
                this.details = details;
            }
        }
    }

    public static class RagEnhancedContext {

        private List<RetrieveResponseDTO.Result> retrievalResults = new ArrayList<>();
        private String mergedContext;

        // Getters and Setters
        public List<RetrieveResponseDTO.Result> getRetrievalResults() {
            return retrievalResults;
        }

        public void setRetrievalResults(List<RetrieveResponseDTO.Result> retrievalResults) {
            this.retrievalResults = retrievalResults;
        }

        public String getMergedContext() {
            return mergedContext;
        }

        public void setMergedContext(String mergedContext) {
            this.mergedContext = mergedContext;
        }
    }

    // 自定义异常类
    public static class PolicyReviewException extends RuntimeException {

        public PolicyReviewException(String message) {
            super(message);
        }

        public PolicyReviewException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
