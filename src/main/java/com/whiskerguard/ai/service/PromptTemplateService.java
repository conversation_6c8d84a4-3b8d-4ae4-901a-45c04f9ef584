package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplate}.
 */
public interface PromptTemplateService {
    /**
     * Save a promptTemplate.
     *
     * @param promptTemplateDTO the entity to save.
     * @return the persisted entity.
     */
    PromptTemplateDTO save(PromptTemplateDTO promptTemplateDTO);

    /**
     * Updates a promptTemplate.
     *
     * @param promptTemplateDTO the entity to update.
     * @return the persisted entity.
     */
    PromptTemplateDTO update(PromptTemplateDTO promptTemplateDTO);

    /**
     * Partially updates a promptTemplate.
     *
     * @param promptTemplateDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PromptTemplateDTO> partialUpdate(PromptTemplateDTO promptTemplateDTO);

    /**
     * Get all the promptTemplates.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PromptTemplateDTO> findAll(Pageable pageable);

    /**
     * Get the "id" promptTemplate.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PromptTemplateDTO> findOne(Long id);

    /**
     * Delete the "id" promptTemplate.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
