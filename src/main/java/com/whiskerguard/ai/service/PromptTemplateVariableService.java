package com.whiskerguard.ai.service;

import com.whiskerguard.ai.service.dto.PromptTemplateVariableDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.ai.domain.PromptTemplateVariable}.
 */
public interface PromptTemplateVariableService {
    /**
     * Save a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO the entity to save.
     * @return the persisted entity.
     */
    PromptTemplateVariableDTO save(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * Updates a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO the entity to update.
     * @return the persisted entity.
     */
    PromptTemplateVariableDTO update(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * Partially updates a promptTemplateVariable.
     *
     * @param promptTemplateVariableDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PromptTemplateVariableDTO> partialUpdate(PromptTemplateVariableDTO promptTemplateVariableDTO);

    /**
     * Get all the promptTemplateVariables.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PromptTemplateVariableDTO> findAll(Pageable pageable);

    /**
     * Get the "id" promptTemplateVariable.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PromptTemplateVariableDTO> findOne(Long id);

    /**
     * Delete the "id" promptTemplateVariable.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
