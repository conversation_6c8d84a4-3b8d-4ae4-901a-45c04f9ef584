package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.service.prompt.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 提示词构建 REST API
 * <p>
 * 提供提示词动态构建相关的API接口，供其他微服务调用。
 * 支持模板化构建、变量验证、预览等功能。
 */
@RestController
@RequestMapping("/api/prompt-builder")
@Tag(name = "提示词构建", description = "提示词动态构建API")
public class PromptBuilderResource {

    private final Logger log = LoggerFactory.getLogger(PromptBuilderResource.class);

    private final PromptBuilderService promptBuilderService;

    public PromptBuilderResource(PromptBuilderService promptBuilderService) {
        this.promptBuilderService = promptBuilderService;
    }

    /**
     * 构建提示词
     */
    @PostMapping("/build")
    @Operation(summary = "构建提示词", description = "根据模板和变量构建完整的提示词")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "构建成功", 
                    content = @Content(schema = @Schema(implementation = PromptBuildResponse.class))),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "模板不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<PromptBuildResponse> buildPrompt(
        @Parameter(description = "提示词构建请求", required = true)
        @Valid @RequestBody PromptBuildRequest request
    ) {
        log.info("收到提示词构建请求 - 模板键: {}, 模板类型: {}, 租户ID: {}", 
            request.getTemplateKey(), request.getTemplateType(), request.getTenantId());

        try {
            long startTime = System.currentTimeMillis();
            
            // 构建提示词
            String prompt = promptBuilderService.buildPrompt(request);
            
            long duration = System.currentTimeMillis() - startTime;

            // 构建响应
            PromptBuildResponse response = new PromptBuildResponse();
            response.setSuccess(true);
            response.setPrompt(prompt);
            response.setTemplateKey(request.getTemplateKey());
            response.setTemplateType(request.getTemplateType());
            response.setBuildDuration(duration);
            response.setCharacterCount(prompt.length());
            response.setEstimatedTokenCount(prompt.length() / 4); // 简单估算

            log.info("提示词构建成功 - 模板: {}, 长度: {}, 耗时: {}ms", 
                request.getTemplateKey(), prompt.length(), duration);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("提示词构建请求参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(PromptBuildResponse.failure("请求参数错误: " + e.getMessage()));
        } catch (Exception e) {
            log.error("提示词构建失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(PromptBuildResponse.failure("构建失败: " + e.getMessage()));
        }
    }

    /**
     * 根据模板键构建提示词（简化接口）
     */
    @PostMapping("/build/{templateKey}")
    @Operation(summary = "根据模板键构建提示词", description = "使用指定模板键和变量构建提示词")
    public ResponseEntity<PromptBuildResponse> buildPromptByKey(
        @Parameter(description = "模板键", required = true) @PathVariable String templateKey,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "变量映射", required = true) @RequestBody Map<String, Object> variables
    ) {
        log.info("收到简化提示词构建请求 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            PromptBuildRequest request = PromptBuildRequest.builder()
                .templateKey(templateKey)
                .tenantId(tenantId)
                .variables(variables)
                .build();

            return buildPrompt(request);

        } catch (Exception e) {
            log.error("简化提示词构建失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(PromptBuildResponse.failure("构建失败: " + e.getMessage()));
        }
    }

    /**
     * 根据模板类型构建提示词
     */
    @PostMapping("/build/type/{templateType}")
    @Operation(summary = "根据模板类型构建提示词", description = "使用指定模板类型和变量构建提示词")
    public ResponseEntity<PromptBuildResponse> buildPromptByType(
        @Parameter(description = "模板类型", required = true) @PathVariable PromptTemplateType templateType,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "变量映射", required = true) @RequestBody Map<String, Object> variables
    ) {
        log.info("收到按类型提示词构建请求 - 模板类型: {}, 租户ID: {}", templateType, tenantId);

        try {
            PromptBuildRequest request = PromptBuildRequest.builder()
                .templateType(templateType)
                .tenantId(tenantId)
                .variables(variables)
                .build();

            return buildPrompt(request);

        } catch (Exception e) {
            log.error("按类型提示词构建失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(PromptBuildResponse.failure("构建失败: " + e.getMessage()));
        }
    }

    /**
     * 验证模板变量
     */
    @PostMapping("/validate/{templateKey}")
    @Operation(summary = "验证模板变量", description = "验证提供的变量是否满足模板要求")
    public ResponseEntity<PromptValidationResult> validateVariables(
        @Parameter(description = "模板键", required = true) @PathVariable String templateKey,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "变量映射", required = true) @RequestBody Map<String, Object> variables
    ) {
        log.info("收到变量验证请求 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            PromptValidationResult result = promptBuilderService.validateVariables(templateKey, tenantId, variables);
            
            log.info("变量验证完成 - 模板: {}, 结果: {}", templateKey, result.isValid());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("变量验证失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(PromptValidationResult.failure("验证失败: " + e.getMessage()));
        }
    }

    /**
     * 获取模板变量信息
     */
    @GetMapping("/variables/{templateKey}")
    @Operation(summary = "获取模板变量信息", description = "获取指定模板所需的变量列表和详细信息")
    public ResponseEntity<PromptVariableInfo> getRequiredVariables(
        @Parameter(description = "模板键", required = true) @PathVariable String templateKey,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId
    ) {
        log.info("收到获取变量信息请求 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            PromptVariableInfo variableInfo = promptBuilderService.getRequiredVariables(templateKey, tenantId);
            
            log.info("获取变量信息成功 - 模板: {}, 变量数量: {}", 
                templateKey, variableInfo.getVariables().size());
            return ResponseEntity.ok(variableInfo);

        } catch (IllegalArgumentException e) {
            log.warn("获取变量信息请求参数错误: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("获取变量信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 预览提示词
     */
    @PostMapping("/preview/{templateKey}")
    @Operation(summary = "预览提示词", description = "预览构建后的提示词内容，用于测试和调试")
    public ResponseEntity<PromptPreviewResult> previewPrompt(
        @Parameter(description = "模板键", required = true) @PathVariable String templateKey,
        @Parameter(description = "租户ID", required = true) @RequestParam Long tenantId,
        @Parameter(description = "变量映射", required = true) @RequestBody Map<String, Object> variables
    ) {
        log.info("收到提示词预览请求 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            PromptPreviewResult result = promptBuilderService.previewPrompt(templateKey, tenantId, variables);
            
            log.info("提示词预览完成 - 模板: {}, 成功: {}, 耗时: {}ms", 
                templateKey, result.isSuccess(), result.getBuildDuration());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("提示词预览失败", e);
            PromptPreviewResult result = PromptPreviewResult.failure("预览失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 提示词构建响应对象
     */
    public static class PromptBuildResponse {
        private boolean success;
        private String prompt;
        private String templateKey;
        private PromptTemplateType templateType;
        private Long buildDuration;
        private Integer characterCount;
        private Integer estimatedTokenCount;
        private String errorMessage;

        public static PromptBuildResponse failure(String errorMessage) {
            PromptBuildResponse response = new PromptBuildResponse();
            response.setSuccess(false);
            response.setErrorMessage(errorMessage);
            return response;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getPrompt() { return prompt; }
        public void setPrompt(String prompt) { this.prompt = prompt; }
        
        public String getTemplateKey() { return templateKey; }
        public void setTemplateKey(String templateKey) { this.templateKey = templateKey; }
        
        public PromptTemplateType getTemplateType() { return templateType; }
        public void setTemplateType(PromptTemplateType templateType) { this.templateType = templateType; }
        
        public Long getBuildDuration() { return buildDuration; }
        public void setBuildDuration(Long buildDuration) { this.buildDuration = buildDuration; }
        
        public Integer getCharacterCount() { return characterCount; }
        public void setCharacterCount(Integer characterCount) { this.characterCount = characterCount; }
        
        public Integer getEstimatedTokenCount() { return estimatedTokenCount; }
        public void setEstimatedTokenCount(Integer estimatedTokenCount) { this.estimatedTokenCount = estimatedTokenCount; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
