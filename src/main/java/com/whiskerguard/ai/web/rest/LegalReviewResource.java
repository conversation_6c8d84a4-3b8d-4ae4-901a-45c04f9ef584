package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.client.dto.LegalReviewRequestDTO;
import com.whiskerguard.ai.client.dto.LegalReviewResponseDTO;
import com.whiskerguard.ai.service.legal.LegalModelService;
import jakarta.validation.Valid;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for legal document review operations
 * 法律文档审查操作的REST控制器
 */
@RestController
@RequestMapping("/api/legal")
public class LegalReviewResource {

    private static final Logger log = LoggerFactory.getLogger(LegalReviewResource.class);

    private final LegalModelService legalModelService;

    public LegalReviewResource(LegalModelService legalModelService) {
        this.legalModelService = legalModelService;
    }

    /**
     * POST /api/legal/review : Review a legal document
     * 审查法律文档
     *
     * @param request the legal review request
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review")
    public ResponseEntity<LegalReviewResponseDTO> reviewDocument(@Valid @RequestBody LegalReviewRequestDTO request) {
        log.debug("REST request to review legal document of type: {}", request.getDocumentType());

        if (request.getDocumentContent() == null || request.getDocumentContent().trim().isEmpty()) {
            log.warn("Empty document content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during legal document review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * POST /api/legal/review/contract : Review a contract specifically
     * 专门审查合同
     *
     * @param contractContent the contract content to review
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review/contract")
    public ResponseEntity<LegalReviewResponseDTO> reviewContract(@RequestBody String contractContent) {
        log.debug("REST request to review contract");

        if (contractContent == null || contractContent.trim().isEmpty()) {
            log.warn("Empty contract content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewRequestDTO request = new LegalReviewRequestDTO();
            request.setDocumentContent(contractContent);
            request.setDocumentType(LegalReviewRequestDTO.DocumentType.CONTRACT);
            request.setPreferredModel(LegalReviewRequestDTO.LegalModelType.AUTO);

            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during contract review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * POST /api/legal/review/policy : Review a company policy specifically
     * 专门审查公司制度
     *
     * @param policyContent the company policy content to review
     * @return the ResponseEntity with status 200 (OK) and the review result in body
     */
    @PostMapping("/review/policy")
    public ResponseEntity<LegalReviewResponseDTO> reviewPolicy(@RequestBody String policyContent) {
        log.debug("REST request to review company policy");

        if (policyContent == null || policyContent.trim().isEmpty()) {
            log.warn("Empty policy content provided for review");
            return ResponseEntity.badRequest().build();
        }

        try {
            LegalReviewRequestDTO request = new LegalReviewRequestDTO();
            request.setDocumentContent(policyContent);
            request.setDocumentType(LegalReviewRequestDTO.DocumentType.POLICY);
            request.setPreferredModel(LegalReviewRequestDTO.LegalModelType.AUTO);

            LegalReviewResponseDTO result = legalModelService.reviewDocument(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during policy review", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * GET /api/legal/models : Get available legal models
     * 获取可用的法律模型
     *
     * @return the ResponseEntity with status 200 (OK) and the list of available models in body
     */
    @GetMapping("/models")
    public ResponseEntity<List<String>> getAvailableModels() {
        log.debug("REST request to get available legal models");

        try {
            List<String> availableModels = legalModelService.getAvailableModels();
            return ResponseEntity.ok(availableModels);
        } catch (Exception e) {
            log.error("Error getting available models", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * GET /api/legal/health : Check health of legal models
     * 检查法律模型的健康状态
     *
     * @return the ResponseEntity with status 200 (OK) if models are healthy
     */
    @GetMapping("/health")
    public ResponseEntity<String> checkHealth() {
        log.debug("REST request to check legal models health");

        try {
            List<String> availableModels = legalModelService.getAvailableModels();
            if (availableModels.isEmpty()) {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body("No legal models are currently available");
            } else {
                return ResponseEntity.ok("Legal models are healthy. Available: " + String.join(", ", availableModels));
            }
        } catch (Exception e) {
            log.error("Error checking legal models health", e);
            return ResponseEntity.internalServerError().body("Error checking legal models health: " + e.getMessage());
        }
    }
}
