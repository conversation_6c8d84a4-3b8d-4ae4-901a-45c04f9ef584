package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.repository.PromptTemplateVersionRepository;
import com.whiskerguard.ai.service.PromptTemplateVersionService;
import com.whiskerguard.ai.service.dto.PromptTemplateVersionDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 提示词模板版本管理
 * REST controller for managing {@link com.whiskerguard.ai.domain.PromptTemplateVersion}.
 */
@RestController
@RequestMapping("/api/prompt-template-versions")
public class PromptTemplateVersionResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromptTemplateVersionResource.class);

    private static final String ENTITY_NAME = "whiskerguardAiServicePromptTemplateVersion";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PromptTemplateVersionService promptTemplateVersionService;

    private final PromptTemplateVersionRepository promptTemplateVersionRepository;

    /**
     * 构造函数
     * Constructor
     *
     * @param promptTemplateVersionService 提示词模板版本服务 prompt template version service
     * @param promptTemplateVersionRepository 提示词模板版本仓库 prompt template version repository
     */
    public PromptTemplateVersionResource(
        PromptTemplateVersionService promptTemplateVersionService,
        PromptTemplateVersionRepository promptTemplateVersionRepository
    ) {
        this.promptTemplateVersionService = promptTemplateVersionService;
        this.promptTemplateVersionRepository = promptTemplateVersionRepository;
    }

    /**
     * {@code POST  /prompt-template-versions} : Create a new promptTemplateVersion.
     *
     * @param promptTemplateVersionDTO the promptTemplateVersionDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new promptTemplateVersionDTO, or with status {@code 400 (Bad Request)} if the promptTemplateVersion has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PromptTemplateVersionDTO> createPromptTemplateVersion(
        @Valid @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save PromptTemplateVersion : {}", promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() != null) {
            throw new BadRequestAlertException("A new promptTemplateVersion cannot already have an ID", ENTITY_NAME, "idexists");
        }
        promptTemplateVersionDTO = promptTemplateVersionService.save(promptTemplateVersionDTO);
        return ResponseEntity.created(new URI("/api/prompt-template-versions/" + promptTemplateVersionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString()))
            .body(promptTemplateVersionDTO);
    }

    /**
     * {@code PUT  /prompt-template-versions/:id} : Updates an existing promptTemplateVersion.
     *
     * @param id the id of the promptTemplateVersionDTO to save.
     * @param promptTemplateVersionDTO the promptTemplateVersionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVersionDTO,
     * or with status {@code 400 (Bad Request)} if the promptTemplateVersionDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the promptTemplateVersionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PromptTemplateVersionDTO> updatePromptTemplateVersion(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PromptTemplateVersion : {}, {}", id, promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVersionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVersionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        promptTemplateVersionDTO = promptTemplateVersionService.update(promptTemplateVersionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString()))
            .body(promptTemplateVersionDTO);
    }

    /**
     * {@code PATCH  /prompt-template-versions/:id} : Partial updates given fields of an existing promptTemplateVersion, field will ignore if it is null
     *
     * @param id the id of the promptTemplateVersionDTO to save.
     * @param promptTemplateVersionDTO the promptTemplateVersionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promptTemplateVersionDTO,
     * or with status {@code 400 (Bad Request)} if the promptTemplateVersionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the promptTemplateVersionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the promptTemplateVersionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PromptTemplateVersionDTO> partialUpdatePromptTemplateVersion(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody PromptTemplateVersionDTO promptTemplateVersionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PromptTemplateVersion partially : {}, {}", id, promptTemplateVersionDTO);
        if (promptTemplateVersionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promptTemplateVersionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promptTemplateVersionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PromptTemplateVersionDTO> result = promptTemplateVersionService.partialUpdate(promptTemplateVersionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, promptTemplateVersionDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /prompt-template-versions} : get all the promptTemplateVersions.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of promptTemplateVersions in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PromptTemplateVersionDTO>> getAllPromptTemplateVersions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PromptTemplateVersions");
        Page<PromptTemplateVersionDTO> page = promptTemplateVersionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /prompt-template-versions/:id} : get the "id" promptTemplateVersion.
     *
     * @param id the id of the promptTemplateVersionDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the promptTemplateVersionDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PromptTemplateVersionDTO> getPromptTemplateVersion(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PromptTemplateVersion : {}", id);
        Optional<PromptTemplateVersionDTO> promptTemplateVersionDTO = promptTemplateVersionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(promptTemplateVersionDTO);
    }

    /**
     * {@code DELETE  /prompt-template-versions/:id} : delete the "id" promptTemplateVersion.
     *
     * @param id the id of the promptTemplateVersionDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePromptTemplateVersion(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PromptTemplateVersion : {}", id);
        promptTemplateVersionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
