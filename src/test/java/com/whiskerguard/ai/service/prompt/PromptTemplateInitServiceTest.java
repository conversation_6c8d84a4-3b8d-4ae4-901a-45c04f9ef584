package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 提示词模板初始化服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class PromptTemplateInitServiceTest {

    @Autowired
    private PromptTemplateRepository promptTemplateRepository;

    @Autowired
    private PromptTemplateInitService promptTemplateInitService;

    @BeforeEach
    void setUp() {
        // 清空数据库
        promptTemplateRepository.deleteAll();
    }

    @Test
    void testInitializeDefaultTemplates() throws Exception {
        // Given
        assertThat(promptTemplateRepository.count()).isEqualTo(0);

        // When
        promptTemplateInitService.run();

        // Then
        // 验证模板总数
        long templateCount = promptTemplateRepository.count();
        assertThat(templateCount).isGreaterThan(0);
        System.out.println("初始化模板总数: " + templateCount);

        // 验证所有枚举类型都有对应的模板
        for (PromptTemplateType templateType : PromptTemplateType.values()) {
            List<PromptTemplate> templates = promptTemplateRepository
                .findByTemplateTypeAndIsSystemDefaultTrueAndStatus(templateType, PromptTemplateStatus.PUBLISHED);
            assertThat(templates).isNotEmpty()
                .withFailMessage("模板类型 %s 没有对应的系统默认模板", templateType);
            System.out.println("✅ " + templateType + " - " + templates.get(0).getName());
        }
    }

    @Test
    void testContractTemplatesInitialization() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        // 验证合同综合审查模板
        Optional<PromptTemplate> contractReviewTemplate = promptTemplateRepository
            .findByTemplateKeyAndIsSystemDefaultTrueAndStatus("CONTRACT_COMPREHENSIVE_REVIEW", PromptTemplateStatus.PUBLISHED)
            .stream().findFirst();
        
        assertThat(contractReviewTemplate).isPresent();
        PromptTemplate template = contractReviewTemplate.get();
        
        assertThat(template.getName()).isEqualTo("合同综合审查模板");
        assertThat(template.getTemplateType()).isEqualTo(PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW);
        assertThat(template.getIsSystemDefault()).isTrue();
        assertThat(template.getStatus()).isEqualTo(PromptTemplateStatus.PUBLISHED);
        assertThat(template.getContent()).contains("合同智能审查任务");
        
        // 验证变量
        assertThat(template.getPromptTemplateVariables()).hasSize(4);
        assertThat(template.getPromptTemplateVariables())
            .extracting(PromptTemplateVariable::getVariableName)
            .contains("CONTRACT_CONTENT", "CONTRACT_TYPE", "COMPANY_INFO", "REVIEW_FOCUS");
    }

    @Test
    void testPolicyTemplatesInitialization() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        // 验证法规转制度模板
        Optional<PromptTemplate> policyTemplate = promptTemplateRepository
            .findByTemplateKeyAndIsSystemDefaultTrueAndStatus("POLICY_REGULATORY_CONVERSION", PromptTemplateStatus.PUBLISHED)
            .stream().findFirst();
        
        assertThat(policyTemplate).isPresent();
        PromptTemplate template = policyTemplate.get();
        
        assertThat(template.getName()).isEqualTo("法规转制度模板");
        assertThat(template.getTemplateType()).isEqualTo(PromptTemplateType.POLICY_REGULATORY_CONVERSION);
        assertThat(template.getContent()).contains("法规到内部管理制度转换任务");
        
        // 验证变量
        assertThat(template.getPromptTemplateVariables()).hasSize(4);
        assertThat(template.getPromptTemplateVariables())
            .extracting(PromptTemplateVariable::getVariableName)
            .contains("LEGAL_TEXT", "COMPANY_NAME", "INDUSTRY", "COMPANY_SIZE");
    }

    @Test
    void testLegalTemplatesInitialization() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        // 验证法律合同审查模板
        Optional<PromptTemplate> legalTemplate = promptTemplateRepository
            .findByTemplateKeyAndIsSystemDefaultTrueAndStatus("LEGAL_CONTRACT_REVIEW", PromptTemplateStatus.PUBLISHED)
            .stream().findFirst();
        
        assertThat(legalTemplate).isPresent();
        PromptTemplate template = legalTemplate.get();
        
        assertThat(template.getName()).isEqualTo("法律合同审查模板");
        assertThat(template.getTemplateType()).isEqualTo(PromptTemplateType.LEGAL_CONTRACT_REVIEW);
        assertThat(template.getContent()).contains("合同法律审查任务");
        
        // 验证变量
        assertThat(template.getPromptTemplateVariables()).hasSize(1);
        assertThat(template.getPromptTemplateVariables())
            .extracting(PromptTemplateVariable::getVariableName)
            .contains("CONTRACT_CONTENT");
    }

    @Test
    void testGeneralTemplatesInitialization() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        // 验证通用分析模板
        Optional<PromptTemplate> generalTemplate = promptTemplateRepository
            .findByTemplateKeyAndIsSystemDefaultTrueAndStatus("GENERAL_ANALYSIS", PromptTemplateStatus.PUBLISHED)
            .stream().findFirst();
        
        assertThat(generalTemplate).isPresent();
        PromptTemplate template = generalTemplate.get();
        
        assertThat(template.getName()).isEqualTo("通用分析模板");
        assertThat(template.getTemplateType()).isEqualTo(PromptTemplateType.GENERAL_ANALYSIS);
        assertThat(template.getContent()).contains("通用文本分析任务");
        
        // 验证变量
        assertThat(template.getPromptTemplateVariables()).hasSize(2);
        assertThat(template.getPromptTemplateVariables())
            .extracting(PromptTemplateVariable::getVariableName)
            .contains("ANALYSIS_CONTENT", "ANALYSIS_TYPE");
    }

    @Test
    void testSkipInitializationWhenDataExists() throws Exception {
        // Given - 先初始化一次
        promptTemplateInitService.run();
        long initialCount = promptTemplateRepository.count();
        assertThat(initialCount).isGreaterThan(0);

        // When - 再次初始化
        promptTemplateInitService.run();

        // Then - 数据量应该保持不变
        long finalCount = promptTemplateRepository.count();
        assertThat(finalCount).isEqualTo(initialCount);
    }

    @Test
    void testTemplateVariableProperties() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        List<PromptTemplate> templates = promptTemplateRepository.findAll();
        assertThat(templates).isNotEmpty();

        for (PromptTemplate template : templates) {
            // 验证模板基本属性
            assertThat(template.getTemplateKey()).isNotBlank();
            assertThat(template.getName()).isNotBlank();
            assertThat(template.getContent()).isNotBlank();
            assertThat(template.getTemplateType()).isNotNull();
            assertThat(template.getStatus()).isEqualTo(PromptTemplateStatus.PUBLISHED);
            assertThat(template.getIsSystemDefault()).isTrue();
            assertThat(template.getCreatedBy()).isEqualTo("system");
            assertThat(template.getCreatedAt()).isNotNull();

            // 验证变量属性
            for (PromptTemplateVariable variable : template.getPromptTemplateVariables()) {
                assertThat(variable.getVariableName()).isNotBlank();
                assertThat(variable.getDisplayName()).isNotBlank();
                assertThat(variable.getVariableType()).isNotNull();
                assertThat(variable.getIsEnabled()).isTrue();
                assertThat(variable.getCreatedBy()).isEqualTo("system");
                assertThat(variable.getCreatedAt()).isNotNull();
                assertThat(variable.getSortOrder()).isNotNull();
            }
        }
    }

    @Test
    void testPrintAllInitializedTemplates() throws Exception {
        // When
        promptTemplateInitService.run();

        // Then
        List<PromptTemplate> templates = promptTemplateRepository.findAll();
        
        System.out.println("\n=== 初始化的模板列表 ===");
        for (PromptTemplate template : templates) {
            System.out.println(String.format(
                "模板: %s (%s) - 变量数: %d",
                template.getName(),
                template.getTemplateKey(),
                template.getPromptTemplateVariables().size()
            ));
            
            for (PromptTemplateVariable variable : template.getPromptTemplateVariables()) {
                System.out.println(String.format(
                    "  - %s (%s) %s %s",
                    variable.getDisplayName(),
                    variable.getVariableName(),
                    variable.getIsRequired() ? "[必填]" : "[可选]",
                    variable.getDefaultValue() != null ? "默认:" + variable.getDefaultValue() : ""
                ));
            }
            System.out.println();
        }
    }
}
