package com.whiskerguard.ai.service.prompt;

import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.domain.enumeration.VariableType;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import com.whiskerguard.ai.service.invocation.RagHelper;
import com.whiskerguard.ai.service.prompt.impl.PromptBuilderServiceImpl;
import com.whiskerguard.ai.service.prompt.impl.PromptVariableResolverImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 提示词构建服务测试
 */
@ExtendWith(MockitoExtension.class)
class PromptBuilderServiceTest {

    @Mock
    private PromptTemplateRepository promptTemplateRepository;

    @Mock
    private RagHelper ragHelper;

    private PromptBuilderService promptBuilderService;
    private PromptVariableResolver variableResolver;

    @BeforeEach
    void setUp() {
        variableResolver = new PromptVariableResolverImpl(null);
        promptBuilderService = new PromptBuilderServiceImpl(
            promptTemplateRepository, 
            variableResolver, 
            ragHelper
        );
    }

    @Test
    void testBuildPromptWithTemplateKey() {
        // Given
        String templateKey = "CONTRACT_COMPREHENSIVE_REVIEW";
        Long tenantId = 1L;
        Map<String, Object> variables = new HashMap<>();
        variables.put("CONTRACT_CONTENT", "这是一份测试合同内容");
        variables.put("CONTRACT_TYPE", "采购合同");

        PromptTemplate template = createTestTemplate(templateKey);
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(templateKey, PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        String result = promptBuilderService.buildPrompt(templateKey, tenantId, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("这是一份测试合同内容");
        assertThat(result).contains("采购合同");
        assertThat(result).contains("测试模板");
    }

    @Test
    void testBuildPromptWithTemplateType() {
        // Given
        PromptTemplateType templateType = PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW;
        Long tenantId = 1L;
        Map<String, Object> variables = new HashMap<>();
        variables.put("CONTRACT_CONTENT", "测试合同内容");

        PromptTemplate template = createTestTemplate("CONTRACT_COMPREHENSIVE_REVIEW");
        when(promptTemplateRepository.findByTemplateTypeAndIsSystemDefaultTrueAndStatus(
            any(), any()))
            .thenReturn(Arrays.asList(template));

        // When
        String result = promptBuilderService.buildPrompt(templateType, tenantId, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("测试合同内容");
    }

    @Test
    void testValidateVariables() {
        // Given
        String templateKey = "CONTRACT_COMPREHENSIVE_REVIEW";
        Long tenantId = 1L;
        Map<String, Object> variables = new HashMap<>();
        variables.put("CONTRACT_CONTENT", "测试合同内容");

        PromptTemplate template = createTestTemplate(templateKey);
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(templateKey, PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        PromptValidationResult result = promptBuilderService.validateVariables(templateKey, tenantId, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isTrue();
    }

    @Test
    void testValidateVariablesMissingRequired() {
        // Given
        String templateKey = "CONTRACT_COMPREHENSIVE_REVIEW";
        Long tenantId = 1L;
        Map<String, Object> variables = new HashMap<>();
        // 缺少必填的 CONTRACT_CONTENT

        PromptTemplate template = createTestTemplate(templateKey);
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(templateKey, PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        PromptValidationResult result = promptBuilderService.validateVariables(templateKey, tenantId, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isFalse();
        assertThat(result.getMissingRequiredVariables()).contains("CONTRACT_CONTENT");
    }

    @Test
    void testGetRequiredVariables() {
        // Given
        String templateKey = "CONTRACT_COMPREHENSIVE_REVIEW";
        Long tenantId = 1L;

        PromptTemplate template = createTestTemplate(templateKey);
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(templateKey, PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        PromptVariableInfo result = promptBuilderService.getRequiredVariables(templateKey, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTemplateKey()).isEqualTo(templateKey);
        assertThat(result.getVariables()).hasSize(2);
        assertThat(result.getVariables().get(0).getVariableName()).isEqualTo("CONTRACT_CONTENT");
    }

    @Test
    void testPreviewPrompt() {
        // Given
        String templateKey = "CONTRACT_COMPREHENSIVE_REVIEW";
        Long tenantId = 1L;
        Map<String, Object> variables = new HashMap<>();
        variables.put("CONTRACT_CONTENT", "测试合同内容");
        variables.put("CONTRACT_TYPE", "采购合同");

        PromptTemplate template = createTestTemplate(templateKey);
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(templateKey, PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        PromptPreviewResult result = promptBuilderService.previewPrompt(templateKey, tenantId, variables);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getPromptContent()).isNotNull();
        assertThat(result.getTemplateKey()).isEqualTo(templateKey);
        assertThat(result.getBuildDuration()).isNotNull();
        assertThat(result.getCharacterCount()).isGreaterThan(0);
    }

    @Test
    void testBuildPromptRequest() {
        // Given
        PromptBuildRequest request = PromptBuildRequest.builder()
            .templateKey("CONTRACT_COMPREHENSIVE_REVIEW")
            .tenantId(1L)
            .variables(Map.of(
                "CONTRACT_CONTENT", "测试合同内容",
                "CONTRACT_TYPE", "采购合同"
            ))
            .enableRagEnhancement(false)
            .build();

        PromptTemplate template = createTestTemplate("CONTRACT_COMPREHENSIVE_REVIEW");
        when(promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus("CONTRACT_COMPREHENSIVE_REVIEW", PromptTemplateStatus.PUBLISHED))
            .thenReturn(Arrays.asList(template));

        // When
        String result = promptBuilderService.buildPrompt(request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).contains("测试合同内容");
        assertThat(result).contains("采购合同");
    }

    /**
     * 创建测试用的模板对象
     */
    private PromptTemplate createTestTemplate(String templateKey) {
        PromptTemplate template = new PromptTemplate();
        template.setId(1L);
        template.setTemplateKey(templateKey);
        template.setName("测试模板");
        template.setDescription("用于测试的模板");
        template.setTemplateType(PromptTemplateType.CONTRACT_COMPREHENSIVE_REVIEW);
        template.setContent("# 测试模板\n\n合同内容：{{CONTRACT_CONTENT}}\n合同类型：{{CONTRACT_TYPE}}\n\n这是一个测试模板。");
        template.setStatus(PromptTemplateStatus.PUBLISHED);
        template.setTemplateVersion(1);
        template.setIsSystemDefault(true);
        template.setTenantId(null);
        template.setUsageCount(0L);
        template.setCreatedBy("test");
        template.setCreatedAt(Instant.now());
        template.setUpdatedBy("test");
        template.setUpdatedAt(Instant.now());
        template.setIsDeleted(false);
        template.setVersion(1);

        // 添加变量
        Set<PromptTemplateVariable> variables = new HashSet<>();
        
        PromptTemplateVariable var1 = new PromptTemplateVariable();
        var1.setId(1L);
        var1.setTenantId(1L);
        var1.setVariableName("CONTRACT_CONTENT");
        var1.setDisplayName("合同内容");
        var1.setDescription("待审查的合同内容");
        var1.setVariableType(VariableType.BUSINESS);
        var1.setIsRequired(true);
        var1.setIsEnabled(true);
        var1.setSortOrder(1);
        var1.setPromptTemplate(template);
        var1.setCreatedBy("test");
        var1.setCreatedAt(Instant.now());
        var1.setUpdatedBy("test");
        var1.setUpdatedAt(Instant.now());
        var1.setIsDeleted(false);
        var1.setVersion(1);
        variables.add(var1);

        PromptTemplateVariable var2 = new PromptTemplateVariable();
        var2.setId(2L);
        var2.setTenantId(1L);
        var2.setVariableName("CONTRACT_TYPE");
        var2.setDisplayName("合同类型");
        var2.setDescription("合同的业务类型");
        var2.setVariableType(VariableType.BUSINESS);
        var2.setIsRequired(false);
        var2.setDefaultValue("通用合同");
        var2.setIsEnabled(true);
        var2.setSortOrder(2);
        var2.setPromptTemplate(template);
        var2.setCreatedBy("test");
        var2.setCreatedAt(Instant.now());
        var2.setUpdatedBy("test");
        var2.setUpdatedAt(Instant.now());
        var2.setIsDeleted(false);
        var2.setVersion(1);
        variables.add(var2);

        template.setPromptTemplateVariables(variables);
        return template;
    }
}
