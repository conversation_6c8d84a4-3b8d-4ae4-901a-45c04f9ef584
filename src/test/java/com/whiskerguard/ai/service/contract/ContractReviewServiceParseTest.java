package com.whiskerguard.ai.service.contract;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.config.EmbeddedSQL;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 合同审查服务解析方法测试
 */
@SpringBootTest
@ActiveProfiles("test")
@EmbeddedSQL
@Transactional
class ContractReviewServiceParseTest {

    @Autowired
    private ContractReviewService contractReviewService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testJsonParsing() throws Exception {
        // 测试 JSON 解析功能
        String jsonResponse = """
            {
              "整体风险等级评估": "中等",
              "具体风险点列表": [
                "服务内容描述不够具体",
                "违约金比例可能过高"
              ]
            }
            """;

        // 直接测试 JSON 解析
        Map<String, Object> resultMap = objectMapper.readValue(jsonResponse, Map.class);

        assertThat(resultMap).isNotNull();
        assertThat(resultMap.get("整体风险等级评估")).isEqualTo("中等");
        assertThat(resultMap.get("具体风险点列表")).isInstanceOf(java.util.List.class);

        @SuppressWarnings("unchecked")
        java.util.List<String> riskPoints = (java.util.List<String>) resultMap.get("具体风险点列表");
        assertThat(riskPoints).hasSize(2);
        assertThat(riskPoints.get(0)).isEqualTo("服务内容描述不够具体");
    }

    @Test
    void testParseEnglishAiResponse() throws Exception {
        // 模拟AI返回的英文JSON响应
        String aiResponse = """
            {
              "overallRiskLevel": "HIGH",
              "riskScore": 75,
              "riskSummary": "Contract has several high-risk issues",
              "recommendations": ["Review payment terms", "Add force majeure clause"],
              "nextActions": ["Legal review required", "Negotiate terms"]
            }
            """;

        // 使用反射调用私有方法进行测试
        Method parseMethod = ContractReviewService.class.getDeclaredMethod(
            "parseAndStructureResult",
            String.class,
            java.util.List.class,
            EnterpriseInfoContext.class,
            LegalRegulatoryContext.class,
            InternalPolicyContext.class,
            Long.class
        );
        parseMethod.setAccessible(true);

        // 调用解析方法
        ContractReviewResponseDTO result = (ContractReviewResponseDTO) parseMethod.invoke(
            contractReviewService,
            aiResponse,
            new ArrayList<>(),
            null,
            null,
            null,
            1001L
        );

        // 验证解析结果
        assertThat(result).isNotNull();
        assertThat(result.getOverallRiskLevel()).isEqualTo(RiskLevel.HIGH);
        assertThat(result.getRiskScore()).isEqualTo(75);
        assertThat(result.getRiskSummary()).isEqualTo("Contract has several high-risk issues");
        assertThat(result.getRecommendations()).containsExactly("Review payment terms", "Add force majeure clause");
        assertThat(result.getNextActions()).containsExactly("Legal review required", "Negotiate terms");
    }
}
